# Advanced Chess AI

A modern, sophisticated chess AI system with hybrid architecture, self-training capabilities, and comprehensive analytics. This project combines classical chess algorithms with modern machine learning techniques to create a competitive chess engine.

## 🚀 Features

### Core Architecture
- **Hybrid Evaluation**: Combines traditional material/positional evaluation with neural networks
- **Minimax + Alpha-Beta Pruning**: Classical search algorithm with optimizations
- **Monte Carlo Tree Search (MCTS)**: Advanced search for multi-step prediction
- **Transposition Tables**: Caching for improved search efficiency

### Advanced Capabilities
- **Self-Training System**: Learns through self-play and improves over time
- **Multi-Step Prediction**: Predicts sequences of moves with confidence scores
- **UCI Protocol Support**: Compatible with chess GUIs and engines
- **Comprehensive Analytics**: Detailed performance analysis and visualization

### Modern Features
- **Neural Network Integration**: Deep learning for position evaluation
- **Real-time Analysis**: Live position assessment and move suggestions
- **Benchmarking Suite**: Test against popular engines like Stockfish
- **Rich Logging**: JSON-based game storage and analysis

## 📋 Requirements

- Python 3.8+
- PyTorch or TensorFlow
- NumPy, Pandas, Matplotlib
- python-chess library
- Stockfish engine (optional, for benchmarking)

## 🛠️ Installation

### Quick Setup

```bash
# Clone the repository
git clone <repository-url>
cd ai-catur

# Run setup script
python setup.py
```

### Manual Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p games models logs analysis tablebases

# Install Stockfish (Ubuntu/Debian)
sudo apt-get install stockfish

# Install Stockfish (macOS)
brew install stockfish
```

## 🎮 Usage

### Interactive Mode
Play against the AI interactively:

```bash
python main.py interactive
```

Commands:
- `move e2e4` - Make a move
- `analyze` - Analyze current position
- `predict` - Predict future moves
- `quit` - Exit

### Self-Training Mode
Train the AI through self-play:

```bash
# Run 5 training iterations with 10 games each
python main.py train --iterations 5 --games 10
```

### Benchmark Mode
Test against other engines:

```bash
# Play 10 games against Stockfish
python main.py benchmark --engine /usr/games/stockfish --games 10 --time 5.0
```

### Analysis Mode
Analyze previous games and performance:

```bash
python main.py analysis --file games/benchmark_results.json
```

### UCI Mode
Use with chess GUIs:

```bash
python main.py uci
```

## 🏗️ Architecture

### Core Components

1. **Chess Engine** (`chess_engine.py`)
   - Material and positional evaluators
   - Minimax search with alpha-beta pruning
   - Neural network integration
   - Hybrid evaluation combining multiple approaches

2. **Monte Carlo Tree Search** (`mcts.py`)
   - MCTS implementation for chess
   - Multi-step prediction capabilities
   - Confidence scoring for moves

3. **Self-Training System** (`self_training.py`)
   - Self-play game generation
   - Neural network training
   - Performance tracking and improvement

4. **UCI Interface** (`uci_interface.py`)
   - Universal Chess Interface protocol
   - Engine communication
   - Game management and result tracking

5. **Analytics System** (`analytics.py`)
   - Performance metrics calculation
   - Game analysis and visualization
   - Comprehensive reporting

### Data Flow

```
Position → Evaluators → Search Algorithms → Best Move
    ↓           ↓              ↓
Analytics ← Game Results ← Move Selection
    ↓
Training Data → Neural Network → Improved Evaluation
```

## 📊 Performance Metrics

The system tracks comprehensive performance metrics:

- **Win Rate**: Percentage of games won
- **Tactical Accuracy**: Quality of tactical moves
- **Positional Understanding**: Strategic play assessment
- **Blunder Rate**: Frequency of significant mistakes
- **Time Management**: Efficiency of time usage
- **Move Quality**: Overall move selection quality

## 🧠 AI Techniques

### Classical Algorithms
- **Minimax**: Game tree search with evaluation
- **Alpha-Beta Pruning**: Search space reduction
- **Transposition Tables**: Position caching
- **Move Ordering**: Improved search efficiency

### Modern Techniques
- **Neural Networks**: Position evaluation learning
- **Monte Carlo Tree Search**: Probabilistic search
- **Self-Play Training**: Automated improvement
- **Ensemble Methods**: Combining multiple evaluators

## 🔧 Configuration

Edit `config.py` to customize:

```python
# AI Settings
config.ai.minimax_depth = 4
config.ai.mcts_simulations = 1000
config.ai.nn_learning_rate = 0.001

# Training Settings
config.ai.self_play_games = 100
config.ai.training_iterations = 10

# Engine Settings
config.engine.stockfish_path = "/usr/games/stockfish"
```

## 📈 Research Applications

This AI is designed for chess research:

1. **Algorithm Comparison**: Test different search and evaluation methods
2. **Learning Curves**: Study how AI improves over time
3. **Engine Analysis**: Compare against established engines
4. **Move Prediction**: Analyze multi-step planning capabilities
5. **Training Efficiency**: Optimize self-learning processes

## 🧪 Testing and Benchmarking

### Running Tests
```bash
# Basic functionality tests
python -m pytest tests/

# Performance benchmarks
python benchmark.py --engine stockfish --games 100
```

### Benchmark Results
Results are saved in JSON format with detailed analysis:

```json
{
  "timestamp": "2024-01-01T12:00:00",
  "engine": "stockfish",
  "games_played": 100,
  "win_rate": 0.45,
  "average_game_length": 42.3,
  "performance_rating": 1650
}
```

## 📁 Project Structure

```
ai-catur/
├── main.py              # Main application entry point
├── config.py            # Configuration settings
├── chess_engine.py      # Core chess engine
├── mcts.py             # Monte Carlo Tree Search
├── self_training.py    # Self-training system
├── uci_interface.py    # UCI protocol implementation
├── analytics.py        # Analysis and visualization
├── setup.py           # Installation script
├── requirements.txt   # Python dependencies
├── games/            # Saved games
├── models/           # Trained neural networks
├── logs/             # Application logs
└── analysis/         # Analysis results and plots
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **python-chess**: Excellent chess library for Python
- **Stockfish**: World-class chess engine for benchmarking
- **PyTorch/TensorFlow**: Deep learning frameworks
- **Chess Programming Community**: Invaluable resources and algorithms

## 📚 Further Reading

- [Chess Programming Wiki](https://www.chessprogramming.org/)
- [AlphaZero Paper](https://arxiv.org/abs/1712.01815)
- [Monte Carlo Tree Search](https://en.wikipedia.org/wiki/Monte_Carlo_tree_search)
- [UCI Protocol](http://wbec-ridderkerk.nl/html/UCIProtocol.html)

---

**Happy Chess Programming! 🏆**
