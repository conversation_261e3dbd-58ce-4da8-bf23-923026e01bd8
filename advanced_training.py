#!/usr/bin/env python3
"""
Advanced Training System with Reinforcement Learning
Menggunakan curriculum learning, adaptive algorithms, dan sophisticated optimization
"""

import numpy as np
import math
import random
import logging
import json
import os
import time
from typing import List, Tuple, Dict, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import deque
import chess
import chess.pgn

from config import config
from advanced_neural_net import AdvancedNeuralEvaluator
from advanced_evaluation import AdvancedMathematicalEvaluator
from chess_engine import MinimaxEngine

logger = logging.getLogger(__name__)

@dataclass
class AdvancedTrainingConfig:
    """Configuration for advanced training"""
    # Reinforcement Learning
    learning_rate: float = 0.001
    discount_factor: float = 0.95
    exploration_rate: float = 0.1
    exploration_decay: float = 0.995
    min_exploration_rate: float = 0.01
    
    # Curriculum Learning
    curriculum_stages: int = 5
    games_per_stage: int = 100
    difficulty_progression: List[float] = None
    
    # Experience Replay
    replay_buffer_size: int = 10000
    batch_size: int = 64
    replay_frequency: int = 10
    
    # Adaptive Learning
    adaptive_lr: bool = True
    lr_decay_factor: float = 0.9
    lr_decay_patience: int = 50
    performance_window: int = 100
    
    # Self-Play Evolution
    population_size: int = 8
    tournament_size: int = 4
    mutation_rate: float = 0.1
    crossover_rate: float = 0.7
    
    def __post_init__(self):
        if self.difficulty_progression is None:
            self.difficulty_progression = [0.2, 0.4, 0.6, 0.8, 1.0]

@dataclass
class Experience:
    """Experience tuple for reinforcement learning"""
    state: str  # Board FEN
    action: str  # Move in UCI format
    reward: float
    next_state: str
    done: bool
    value_estimate: float = 0.0
    policy_probs: List[float] = None

class ExperienceReplay:
    """Experience replay buffer for training"""
    
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)
        self.capacity = capacity
    
    def add(self, experience: Experience):
        """Add experience to buffer"""
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> List[Experience]:
        """Sample random batch from buffer"""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        return random.sample(list(self.buffer), batch_size)
    
    def size(self) -> int:
        return len(self.buffer)
    
    def clear(self):
        self.buffer.clear()

class CurriculumLearning:
    """Curriculum learning for progressive difficulty"""
    
    def __init__(self, config: AdvancedTrainingConfig):
        self.config = config
        self.current_stage = 0
        self.games_in_stage = 0
        self.stage_performance = []
    
    def get_current_difficulty(self) -> float:
        """Get current training difficulty"""
        if self.current_stage >= len(self.config.difficulty_progression):
            return self.config.difficulty_progression[-1]
        return self.config.difficulty_progression[self.current_stage]
    
    def should_advance_stage(self, recent_performance: float) -> bool:
        """Check if should advance to next curriculum stage"""
        if self.games_in_stage < self.config.games_per_stage:
            return False
        
        # Advance if performance is good enough
        difficulty = self.get_current_difficulty()
        threshold = 0.4 + (difficulty * 0.3)  # Dynamic threshold
        
        return recent_performance >= threshold
    
    def advance_stage(self):
        """Advance to next curriculum stage"""
        if self.current_stage < len(self.config.difficulty_progression) - 1:
            self.current_stage += 1
            self.games_in_stage = 0
            self.stage_performance.clear()
            logger.info(f"Advanced to curriculum stage {self.current_stage}, difficulty: {self.get_current_difficulty()}")
    
    def record_game_result(self, result: float):
        """Record game result for curriculum progression"""
        self.games_in_stage += 1
        self.stage_performance.append(result)
        
        # Check for stage advancement
        if len(self.stage_performance) >= 20:  # Need some games for reliable estimate
            recent_avg = np.mean(self.stage_performance[-20:])
            if self.should_advance_stage(recent_avg):
                self.advance_stage()

class AdaptiveLearningRate:
    """Adaptive learning rate scheduler"""
    
    def __init__(self, initial_lr: float, decay_factor: float = 0.9, patience: int = 50):
        self.initial_lr = initial_lr
        self.current_lr = initial_lr
        self.decay_factor = decay_factor
        self.patience = patience
        self.best_performance = -float('inf')
        self.wait_count = 0
        self.performance_history = deque(maxlen=100)
    
    def update(self, performance: float) -> float:
        """Update learning rate based on performance"""
        self.performance_history.append(performance)
        
        if performance > self.best_performance:
            self.best_performance = performance
            self.wait_count = 0
        else:
            self.wait_count += 1
        
        # Decay learning rate if no improvement
        if self.wait_count >= self.patience:
            self.current_lr *= self.decay_factor
            self.wait_count = 0
            logger.info(f"Learning rate decayed to {self.current_lr:.6f}")
        
        return self.current_lr
    
    def get_lr(self) -> float:
        return self.current_lr

class EvolutionaryOptimizer:
    """Evolutionary optimization for neural network weights"""
    
    def __init__(self, config: AdvancedTrainingConfig):
        self.config = config
        self.population = []
        self.fitness_scores = []
    
    def initialize_population(self, base_evaluator: AdvancedNeuralEvaluator):
        """Initialize population with mutations of base evaluator"""
        self.population = []
        
        for _ in range(self.config.population_size):
            # Create mutated copy
            mutated_evaluator = self._mutate_evaluator(base_evaluator)
            self.population.append(mutated_evaluator)
    
    def _mutate_evaluator(self, evaluator: AdvancedNeuralEvaluator) -> AdvancedNeuralEvaluator:
        """Create mutated version of evaluator"""
        # For now, return copy (would implement actual mutation with PyTorch)
        return evaluator
    
    def evaluate_population(self, test_positions: List[chess.Board]) -> List[float]:
        """Evaluate fitness of entire population"""
        fitness_scores = []
        
        for evaluator in self.population:
            fitness = self._calculate_fitness(evaluator, test_positions)
            fitness_scores.append(fitness)
        
        self.fitness_scores = fitness_scores
        return fitness_scores
    
    def _calculate_fitness(self, evaluator: AdvancedNeuralEvaluator, positions: List[chess.Board]) -> float:
        """Calculate fitness score for evaluator"""
        total_score = 0.0
        
        for board in positions:
            try:
                evaluation = evaluator.evaluate(board)
                # Fitness based on evaluation consistency and magnitude
                total_score += abs(evaluation) * 0.1 + (1.0 / (1.0 + abs(evaluation - 0)))
            except:
                total_score -= 10  # Penalty for errors
        
        return total_score / len(positions) if positions else 0
    
    def select_parents(self) -> Tuple[int, int]:
        """Tournament selection for parents"""
        tournament_indices = random.sample(range(len(self.population)), self.config.tournament_size)
        tournament_fitness = [self.fitness_scores[i] for i in tournament_indices]
        
        # Select two best from tournament
        sorted_indices = sorted(tournament_indices, key=lambda i: self.fitness_scores[i], reverse=True)
        return sorted_indices[0], sorted_indices[1]
    
    def evolve_generation(self) -> AdvancedNeuralEvaluator:
        """Evolve population for one generation"""
        new_population = []
        
        # Keep best individuals (elitism)
        elite_count = max(1, self.config.population_size // 4)
        elite_indices = sorted(range(len(self.fitness_scores)), 
                             key=lambda i: self.fitness_scores[i], reverse=True)[:elite_count]
        
        for idx in elite_indices:
            new_population.append(self.population[idx])
        
        # Generate offspring
        while len(new_population) < self.config.population_size:
            parent1_idx, parent2_idx = self.select_parents()
            
            if random.random() < self.config.crossover_rate:
                offspring = self._crossover(self.population[parent1_idx], self.population[parent2_idx])
            else:
                offspring = self.population[parent1_idx]
            
            if random.random() < self.config.mutation_rate:
                offspring = self._mutate_evaluator(offspring)
            
            new_population.append(offspring)
        
        self.population = new_population
        return self.population[elite_indices[0]]  # Return best individual
    
    def _crossover(self, parent1: AdvancedNeuralEvaluator, parent2: AdvancedNeuralEvaluator) -> AdvancedNeuralEvaluator:
        """Crossover two evaluators"""
        # For now, return parent1 (would implement actual crossover with PyTorch)
        return parent1

class AdvancedReinforcementTrainer:
    """Advanced reinforcement learning trainer"""
    
    def __init__(self, config: AdvancedTrainingConfig = None):
        self.config = config or AdvancedTrainingConfig()
        self.experience_replay = ExperienceReplay(self.config.replay_buffer_size)
        self.curriculum = CurriculumLearning(self.config)
        self.adaptive_lr = AdaptiveLearningRate(self.config.learning_rate)
        self.evolutionary_optimizer = EvolutionaryOptimizer(self.config)
        
        # Initialize evaluators
        self.neural_evaluator = AdvancedNeuralEvaluator()
        self.math_evaluator = AdvancedMathematicalEvaluator()
        self.minimax_engine = MinimaxEngine(self.math_evaluator)
        
        # Training statistics
        self.training_stats = {
            'games_played': 0,
            'total_reward': 0.0,
            'win_rate': 0.0,
            'average_game_length': 0.0,
            'exploration_rate': self.config.exploration_rate,
            'learning_rate': self.config.learning_rate,
            'curriculum_stage': 0
        }
        
        logger.info("Advanced reinforcement trainer initialized")
    
    def train(self, num_iterations: int, games_per_iteration: int = 50) -> Dict[str, Any]:
        """Main training loop"""
        logger.info(f"Starting advanced training: {num_iterations} iterations, {games_per_iteration} games each")
        
        training_results = []
        
        for iteration in range(num_iterations):
            logger.info(f"Training iteration {iteration + 1}/{num_iterations}")
            
            # Self-play games
            iteration_results = self._run_self_play_iteration(games_per_iteration)
            
            # Experience replay training
            if self.experience_replay.size() >= self.config.batch_size:
                replay_results = self._train_from_experience_replay()
                iteration_results.update(replay_results)
            
            # Evolutionary optimization (every few iterations)
            if iteration % 5 == 0 and iteration > 0:
                evolution_results = self._run_evolutionary_optimization()
                iteration_results.update(evolution_results)
            
            # Update adaptive learning rate
            performance = iteration_results.get('win_rate', 0.0)
            new_lr = self.adaptive_lr.update(performance)
            self.training_stats['learning_rate'] = new_lr
            
            # Update exploration rate
            self.config.exploration_rate = max(
                self.config.min_exploration_rate,
                self.config.exploration_rate * self.config.exploration_decay
            )
            self.training_stats['exploration_rate'] = self.config.exploration_rate
            
            # Record results
            iteration_results['iteration'] = iteration + 1
            iteration_results['curriculum_stage'] = self.curriculum.current_stage
            iteration_results['exploration_rate'] = self.config.exploration_rate
            iteration_results['learning_rate'] = new_lr
            
            training_results.append(iteration_results)
            
            # Save progress
            if iteration % 10 == 0:
                self._save_training_progress(training_results)
            
            logger.info(f"Iteration {iteration + 1} completed: Win rate: {performance:.3f}, "
                       f"LR: {new_lr:.6f}, Exploration: {self.config.exploration_rate:.3f}")
        
        # Final save
        self._save_training_progress(training_results)
        
        return {
            'training_results': training_results,
            'final_stats': self.training_stats,
            'total_iterations': num_iterations
        }
    
    def _run_self_play_iteration(self, num_games: int) -> Dict[str, Any]:
        """Run self-play games for one iteration"""
        game_results = []
        total_reward = 0.0
        total_game_length = 0
        
        for game_num in range(num_games):
            # Adjust opponent strength based on curriculum
            difficulty = self.curriculum.get_current_difficulty()
            opponent_depth = max(1, int(difficulty * 4))
            
            # Play game
            result = self._play_self_play_game(opponent_depth)
            game_results.append(result)
            
            # Update curriculum
            game_reward = 1.0 if result['result'] == 'win' else (0.5 if result['result'] == 'draw' else 0.0)
            self.curriculum.record_game_result(game_reward)
            
            total_reward += game_reward
            total_game_length += result['game_length']
        
        # Calculate statistics
        win_rate = sum(1 for r in game_results if r['result'] == 'win') / num_games
        avg_game_length = total_game_length / num_games
        avg_reward = total_reward / num_games
        
        # Update training stats
        self.training_stats['games_played'] += num_games
        self.training_stats['total_reward'] += total_reward
        self.training_stats['win_rate'] = win_rate
        self.training_stats['average_game_length'] = avg_game_length
        self.training_stats['curriculum_stage'] = self.curriculum.current_stage
        
        return {
            'games_played': num_games,
            'win_rate': win_rate,
            'average_game_length': avg_game_length,
            'average_reward': avg_reward,
            'curriculum_difficulty': self.curriculum.get_current_difficulty()
        }
    
    def _play_self_play_game(self, opponent_depth: int) -> Dict[str, Any]:
        """Play a single self-play game"""
        board = chess.Board()
        game_experiences = []
        move_count = 0
        
        while not board.is_game_over() and move_count < 200:
            # Choose move based on exploration/exploitation
            if random.random() < self.config.exploration_rate:
                # Exploration: random legal move
                move = random.choice(list(board.legal_moves))
            else:
                # Exploitation: use current best strategy
                move = self._select_best_move(board)
            
            # Record experience
            state_fen = board.fen()
            board.push(move)
            next_state_fen = board.fen()
            
            # Calculate immediate reward
            reward = self._calculate_immediate_reward(board, move)
            
            experience = Experience(
                state=state_fen,
                action=move.uci(),
                reward=reward,
                next_state=next_state_fen,
                done=board.is_game_over()
            )
            
            game_experiences.append(experience)
            move_count += 1
        
        # Calculate final game result
        game_result = self._determine_game_result(board)
        
        # Backpropagate rewards
        self._backpropagate_rewards(game_experiences, game_result)
        
        # Add experiences to replay buffer
        for exp in game_experiences:
            self.experience_replay.add(exp)
        
        return {
            'result': game_result,
            'game_length': move_count,
            'experiences': len(game_experiences)
        }
    
    def _select_best_move(self, board: chess.Board) -> chess.Move:
        """Select best move using current strategy"""
        # Use minimax with advanced evaluation
        result = self.minimax_engine.search(board, depth=3)
        return result.move if result.move else random.choice(list(board.legal_moves))
    
    def _calculate_immediate_reward(self, board: chess.Board, move: chess.Move) -> float:
        """Calculate immediate reward for a move"""
        reward = 0.0
        
        # Capture reward
        if board.is_capture(move):
            captured_piece = board.piece_at(move.to_square)
            if captured_piece:
                piece_values = {chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
                              chess.ROOK: 5, chess.QUEEN: 9}
                reward += piece_values.get(captured_piece.piece_type, 0) * 0.1
        
        # Check reward
        if board.is_check():
            reward += 0.05
        
        # Checkmate reward
        if board.is_checkmate():
            reward += 10.0
        
        return reward
    
    def _determine_game_result(self, board: chess.Board) -> str:
        """Determine final game result"""
        if board.is_checkmate():
            return 'win' if not board.turn else 'loss'  # Previous player won
        elif board.is_stalemate() or board.is_insufficient_material():
            return 'draw'
        else:
            return 'draw'  # Game too long
    
    def _backpropagate_rewards(self, experiences: List[Experience], final_result: str):
        """Backpropagate final game result through experiences"""
        final_reward = 1.0 if final_result == 'win' else (0.0 if final_result == 'loss' else 0.5)
        
        # Discount rewards backwards
        discounted_reward = final_reward
        for exp in reversed(experiences):
            exp.reward += discounted_reward * self.config.discount_factor
            discounted_reward *= self.config.discount_factor
    
    def _train_from_experience_replay(self) -> Dict[str, Any]:
        """Train from experience replay buffer"""
        batch = self.experience_replay.sample(self.config.batch_size)
        
        # For now, just return statistics (would implement actual neural network training)
        avg_reward = np.mean([exp.reward for exp in batch])
        
        return {
            'replay_batch_size': len(batch),
            'average_batch_reward': avg_reward
        }
    
    def _run_evolutionary_optimization(self) -> Dict[str, Any]:
        """Run evolutionary optimization"""
        # Generate test positions
        test_positions = self._generate_test_positions(50)
        
        # Initialize population if needed
        if not self.evolutionary_optimizer.population:
            self.evolutionary_optimizer.initialize_population(self.neural_evaluator)
        
        # Evaluate population
        fitness_scores = self.evolutionary_optimizer.evaluate_population(test_positions)
        
        # Evolve
        best_individual = self.evolutionary_optimizer.evolve_generation()
        
        return {
            'evolution_generation': True,
            'best_fitness': max(fitness_scores),
            'average_fitness': np.mean(fitness_scores),
            'population_size': len(self.evolutionary_optimizer.population)
        }
    
    def _generate_test_positions(self, count: int) -> List[chess.Board]:
        """Generate test positions for evaluation"""
        positions = []
        
        for _ in range(count):
            board = chess.Board()
            # Make random moves to get diverse positions
            for _ in range(random.randint(5, 30)):
                if board.is_game_over():
                    break
                move = random.choice(list(board.legal_moves))
                board.push(move)
            positions.append(board.copy())
        
        return positions
    
    def _save_training_progress(self, results: List[Dict[str, Any]]):
        """Save training progress to file"""
        progress_data = {
            'timestamp': time.time(),
            'training_stats': self.training_stats,
            'results': results,
            'config': asdict(self.config)
        }
        
        os.makedirs('models', exist_ok=True)
        with open('models/advanced_training_progress.json', 'w') as f:
            json.dump(progress_data, f, indent=2)

# Export main class
__all__ = ['AdvancedReinforcementTrainer', 'AdvancedTrainingConfig']
