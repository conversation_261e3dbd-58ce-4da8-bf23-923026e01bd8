#!/usr/bin/env python3
"""
Advanced Neural Network Architecture for Chess AI
Menggunakan matematika kompleks modern: Transformer, Attention, Fourier Analysis
"""

import numpy as np
import math
import logging
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass
import json

# Optional imports with fallbacks
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.nn import MultiheadAttention, TransformerEncoder, TransformerEncoderLayer
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available, using mathematical fallbacks")

import chess
from config import config

logger = logging.getLogger(__name__)

@dataclass
class AdvancedNetworkConfig:
    """Configuration for advanced neural network"""
    board_size: int = 8
    piece_types: int = 12  # 6 pieces x 2 colors
    embedding_dim: int = 512
    num_heads: int = 8
    num_layers: int = 6
    dropout: float = 0.1
    fourier_components: int = 64
    attention_window: int = 64
    use_positional_encoding: bool = True
    use_fourier_features: bool = True
    use_complex_attention: bool = True

class ComplexMathUtils:
    """Advanced mathematical utilities for chess evaluation"""
    
    @staticmethod
    def fourier_transform_board(board_tensor: np.ndarray, components: int = 64) -> np.ndarray:
        """Apply Fourier transform to board representation for frequency analysis"""
        # Flatten board to 1D for FFT
        flat_board = board_tensor.flatten()
        
        # Apply FFT and take magnitude
        fft_result = np.fft.fft(flat_board, n=components)
        magnitude = np.abs(fft_result)
        phase = np.angle(fft_result)
        
        # Combine magnitude and phase information
        fourier_features = np.concatenate([magnitude, phase])
        return fourier_features
    
    @staticmethod
    def complex_attention_weights(query: np.ndarray, key: np.ndarray, 
                                value: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Complex attention mechanism using quaternion-like operations"""
        # Normalize inputs
        query_norm = query / (np.linalg.norm(query, axis=-1, keepdims=True) + 1e-8)
        key_norm = key / (np.linalg.norm(key, axis=-1, keepdims=True) + 1e-8)
        
        # Complex attention scores using dot product and cross product
        dot_scores = np.sum(query_norm * key_norm, axis=-1)
        
        # Add rotational component for chess-specific spatial awareness
        cross_component = np.sum(np.cross(query_norm, key_norm, axis=-1), axis=-1)
        
        # Combine linear and rotational components
        attention_scores = dot_scores + 0.1 * cross_component
        attention_weights = ComplexMathUtils.softmax(attention_scores)
        
        # Apply attention to values
        attended_values = np.sum(attention_weights[..., np.newaxis] * value, axis=-2)
        
        return attended_values, attention_weights
    
    @staticmethod
    def softmax(x: np.ndarray, temperature: float = 1.0) -> np.ndarray:
        """Numerically stable softmax with temperature"""
        x_scaled = x / temperature
        exp_x = np.exp(x_scaled - np.max(x_scaled, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    @staticmethod
    def positional_encoding(position: int, d_model: int) -> np.ndarray:
        """Sinusoidal positional encoding for chess squares"""
        pe = np.zeros(d_model)
        
        for i in range(0, d_model, 2):
            pe[i] = math.sin(position / (10000 ** (i / d_model)))
            if i + 1 < d_model:
                pe[i + 1] = math.cos(position / (10000 ** (i / d_model)))
        
        return pe
    
    @staticmethod
    def chess_specific_encoding(square: int, piece: int, d_model: int) -> np.ndarray:
        """Chess-specific positional encoding considering piece type and square"""
        # Basic positional encoding
        pos_enc = ComplexMathUtils.positional_encoding(square, d_model // 2)
        
        # Piece-specific encoding
        piece_enc = ComplexMathUtils.positional_encoding(piece * 8 + square, d_model // 2)
        
        return np.concatenate([pos_enc, piece_enc])

class AdvancedBoardEncoder:
    """Advanced board representation with multiple encoding schemes"""
    
    def __init__(self, config: AdvancedNetworkConfig):
        self.config = config
        self.piece_to_index = {
            chess.PAWN: 0, chess.ROOK: 1, chess.KNIGHT: 2,
            chess.BISHOP: 3, chess.QUEEN: 4, chess.KING: 5
        }
    
    def encode_board_advanced(self, board: chess.Board) -> Dict[str, np.ndarray]:
        """Advanced multi-modal board encoding"""
        encodings = {}
        
        # 1. Basic piece placement encoding
        piece_planes = self._encode_piece_planes(board)
        encodings['piece_planes'] = piece_planes
        
        # 2. Spatial relationship encoding
        spatial_features = self._encode_spatial_relationships(board)
        encodings['spatial_features'] = spatial_features
        
        # 3. Temporal features (game phase, move history)
        temporal_features = self._encode_temporal_features(board)
        encodings['temporal_features'] = temporal_features
        
        # 4. Fourier transform features
        if self.config.use_fourier_features:
            fourier_features = ComplexMathUtils.fourier_transform_board(
                piece_planes, self.config.fourier_components
            )
            encodings['fourier_features'] = fourier_features
        
        # 5. Positional encodings for each square
        if self.config.use_positional_encoding:
            positional_encodings = self._encode_positional_features(board)
            encodings['positional_encodings'] = positional_encodings
        
        return encodings
    
    def _encode_piece_planes(self, board: chess.Board) -> np.ndarray:
        """Encode board as piece planes (12 planes for 6 pieces x 2 colors)"""
        planes = np.zeros((12, 8, 8), dtype=np.float32)
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                piece_idx = self.piece_to_index[piece.piece_type]
                color_offset = 0 if piece.color == chess.WHITE else 6
                plane_idx = piece_idx + color_offset
                
                row, col = divmod(square, 8)
                planes[plane_idx, row, col] = 1.0
        
        return planes
    
    def _encode_spatial_relationships(self, board: chess.Board) -> np.ndarray:
        """Encode spatial relationships between pieces"""
        features = []
        
        # King safety features
        white_king_square = board.king(chess.WHITE)
        black_king_square = board.king(chess.BLACK)
        
        if white_king_square and black_king_square:
            king_distance = chess.square_distance(white_king_square, black_king_square)
            features.append(king_distance / 7.0)  # Normalize
            
            # King centralization
            white_king_center_dist = min(
                chess.square_distance(white_king_square, chess.D4),
                chess.square_distance(white_king_square, chess.E4),
                chess.square_distance(white_king_square, chess.D5),
                chess.square_distance(white_king_square, chess.E5)
            )
            black_king_center_dist = min(
                chess.square_distance(black_king_square, chess.D4),
                chess.square_distance(black_king_square, chess.E4),
                chess.square_distance(black_king_square, chess.D5),
                chess.square_distance(black_king_square, chess.E5)
            )
            
            features.extend([
                white_king_center_dist / 4.0,
                black_king_center_dist / 4.0
            ])
        else:
            features.extend([0.0, 0.0, 0.0])
        
        # Piece coordination features
        piece_coordination = self._calculate_piece_coordination(board)
        features.extend(piece_coordination)
        
        return np.array(features, dtype=np.float32)
    
    def _calculate_piece_coordination(self, board: chess.Board) -> List[float]:
        """Calculate piece coordination and harmony"""
        coordination_features = []
        
        for color in [chess.WHITE, chess.BLACK]:
            pieces = []
            for square in chess.SQUARES:
                piece = board.piece_at(square)
                if piece and piece.color == color:
                    pieces.append((square, piece.piece_type))
            
            if len(pieces) < 2:
                coordination_features.extend([0.0, 0.0])
                continue
            
            # Average distance between pieces
            total_distance = 0
            count = 0
            for i, (sq1, _) in enumerate(pieces):
                for sq2, _ in pieces[i+1:]:
                    total_distance += chess.square_distance(sq1, sq2)
                    count += 1
            
            avg_distance = total_distance / count if count > 0 else 0
            coordination_features.append(avg_distance / 7.0)  # Normalize
            
            # Piece centralization
            center_squares = [chess.D4, chess.E4, chess.D5, chess.E5]
            centralization = 0
            for square, piece_type in pieces:
                if piece_type != chess.KING:  # Exclude king from centralization
                    min_center_dist = min(chess.square_distance(square, cs) for cs in center_squares)
                    centralization += (4 - min_center_dist) / 4.0
            
            avg_centralization = centralization / len(pieces) if pieces else 0
            coordination_features.append(avg_centralization)
        
        return coordination_features
    
    def _encode_temporal_features(self, board: chess.Board) -> np.ndarray:
        """Encode temporal aspects of the position"""
        features = []
        
        # Game phase estimation
        material_count = len([sq for sq in chess.SQUARES if board.piece_at(sq)])
        game_phase = 1.0 - (material_count - 2) / 30.0  # Normalize (2 kings minimum)
        features.append(max(0.0, min(1.0, game_phase)))
        
        # Castling rights
        features.extend([
            float(board.has_kingside_castling_rights(chess.WHITE)),
            float(board.has_queenside_castling_rights(chess.WHITE)),
            float(board.has_kingside_castling_rights(chess.BLACK)),
            float(board.has_queenside_castling_rights(chess.BLACK))
        ])
        
        # En passant
        features.append(float(board.ep_square is not None))
        
        # Move count (normalized)
        features.append(min(board.fullmove_number / 100.0, 1.0))
        
        return np.array(features, dtype=np.float32)
    
    def _encode_positional_features(self, board: chess.Board) -> np.ndarray:
        """Encode positional features for each square"""
        positional_features = np.zeros((64, self.config.embedding_dim), dtype=np.float32)
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            piece_type = piece.piece_type if piece else 0
            
            # Generate chess-specific positional encoding
            pos_encoding = ComplexMathUtils.chess_specific_encoding(
                square, piece_type, self.config.embedding_dim
            )
            positional_features[square] = pos_encoding
        
        return positional_features

if TORCH_AVAILABLE:
    class AdvancedChessTransformer(nn.Module):
        """Advanced Transformer architecture for chess position evaluation"""
        
        def __init__(self, config: AdvancedNetworkConfig):
            super().__init__()
            self.config = config
            self.encoder = AdvancedBoardEncoder(config)
            
            # Embedding layers
            self.piece_embedding = nn.Embedding(13, config.embedding_dim)  # 12 pieces + empty
            self.square_embedding = nn.Embedding(64, config.embedding_dim)
            
            # Transformer layers
            encoder_layer = TransformerEncoderLayer(
                d_model=config.embedding_dim,
                nhead=config.num_heads,
                dropout=config.dropout,
                batch_first=True
            )
            self.transformer = TransformerEncoder(encoder_layer, config.num_layers)
            
            # Output layers
            self.value_head = nn.Sequential(
                nn.Linear(config.embedding_dim, 512),
                nn.ReLU(),
                nn.Dropout(config.dropout),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, 1),
                nn.Tanh()
            )
            
            # Policy head for move prediction
            self.policy_head = nn.Sequential(
                nn.Linear(config.embedding_dim, 512),
                nn.ReLU(),
                nn.Dropout(config.dropout),
                nn.Linear(512, 4096)  # All possible moves
            )
        
        def forward(self, board_encoding: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
            """Forward pass through the transformer"""
            # Apply transformer
            transformer_output = self.transformer(board_encoding)
            
            # Global average pooling
            pooled_output = torch.mean(transformer_output, dim=1)
            
            # Value and policy predictions
            value = self.value_head(pooled_output)
            policy = self.policy_head(pooled_output)
            
            return value, policy

else:
    class AdvancedChessTransformer:
        """Fallback implementation without PyTorch"""
        
        def __init__(self, config: AdvancedNetworkConfig):
            self.config = config
            self.encoder = AdvancedBoardEncoder(config)
            logger.warning("Using fallback transformer implementation")
        
        def evaluate_position(self, board: chess.Board) -> float:
            """Fallback position evaluation using mathematical features"""
            encodings = self.encoder.encode_board_advanced(board)
            
            # Simple weighted combination of features
            score = 0.0
            
            # Material balance
            piece_planes = encodings['piece_planes']
            material_balance = np.sum(piece_planes[:6]) - np.sum(piece_planes[6:])
            score += material_balance * 100
            
            # Spatial features
            spatial_features = encodings['spatial_features']
            score += np.sum(spatial_features) * 50
            
            # Fourier features (if available)
            if 'fourier_features' in encodings:
                fourier_magnitude = np.mean(encodings['fourier_features'][:self.config.fourier_components])
                score += fourier_magnitude * 25
            
            return np.tanh(score / 1000.0) * 1000  # Normalize and scale

class AdvancedNeuralEvaluator:
    """Advanced neural evaluator using complex mathematics"""
    
    def __init__(self, model_path: Optional[str] = None):
        self.config = AdvancedNetworkConfig()
        
        if TORCH_AVAILABLE:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.model = AdvancedChessTransformer(self.config)
            
            if model_path and os.path.exists(model_path):
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            
            self.model.to(self.device)
            self.model.eval()
        else:
            self.model = AdvancedChessTransformer(self.config)
        
        self.encoder = AdvancedBoardEncoder(self.config)
        logger.info(f"Advanced neural evaluator initialized (PyTorch: {TORCH_AVAILABLE})")
    
    def evaluate(self, board: chess.Board) -> float:
        """Evaluate position using advanced neural network"""
        if TORCH_AVAILABLE:
            return self._evaluate_with_torch(board)
        else:
            return self.model.evaluate_position(board)
    
    def _evaluate_with_torch(self, board: chess.Board) -> float:
        """Evaluate using PyTorch model"""
        try:
            # Encode board
            encodings = self.encoder.encode_board_advanced(board)
            
            # Convert to tensor
            board_tensor = self._encodings_to_tensor(encodings)
            board_tensor = board_tensor.unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                value, _ = self.model(board_tensor)
                score = value.item() * 1000  # Scale to centipawns
            
            return score
        
        except Exception as e:
            logger.error(f"Neural evaluation failed: {e}")
            return self.model.evaluate_position(board)
    
    def _encodings_to_tensor(self, encodings: Dict[str, np.ndarray]):
        """Convert encodings to tensor format"""
        # Combine all features into a sequence
        features = []
        
        # Flatten piece planes and add positional encoding
        piece_planes = encodings['piece_planes'].flatten()
        if 'positional_encodings' in encodings:
            pos_enc = encodings['positional_encodings'].flatten()
            combined = np.concatenate([piece_planes, pos_enc])
        else:
            combined = piece_planes
        
        # Reshape to sequence format
        seq_len = min(64, len(combined) // self.config.embedding_dim)
        if len(combined) >= seq_len * self.config.embedding_dim:
            features = combined[:seq_len * self.config.embedding_dim].reshape(seq_len, self.config.embedding_dim)
        else:
            # Pad if necessary
            padded = np.zeros((seq_len, self.config.embedding_dim))
            padded.flat[:len(combined)] = combined
            features = padded
        
        if TORCH_AVAILABLE:
            return torch.FloatTensor(features)
        else:
            return features

# Export the main class
__all__ = ['AdvancedNeuralEvaluator', 'AdvancedNetworkConfig', 'ComplexMathUtils']
