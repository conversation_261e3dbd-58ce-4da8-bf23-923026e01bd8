"""
Configuration file for the Advanced Chess AI System
"""
from dataclasses import dataclass
from typing import Dict, List, Optional
import os

@dataclass
class EngineConfig:
    """Configuration for chess engines"""
    stockfish_path: str = "/usr/games/stockfish"  # Default Ubuntu path
    stockfish_depth: int = 15
    stockfish_time: float = 1.0
    
    # Engine levels for testing
    engine_levels: Dict[str, int] = None
    
    def __post_init__(self):
        if self.engine_levels is None:
            self.engine_levels = {
                "beginner": 1,
                "intermediate": 5,
                "advanced": 10,
                "expert": 15,
                "master": 20
            }

@dataclass
class AIConfig:
    """Configuration for AI components"""
    # Minimax settings
    minimax_depth: int = 4
    use_alpha_beta: bool = True
    use_transposition_table: bool = True
    
    # Neural Network settings
    nn_hidden_layers: List[int] = None
    nn_learning_rate: float = 0.001
    nn_batch_size: int = 32
    nn_epochs: int = 100
    
    # MCTS settings
    mcts_simulations: int = 1000
    mcts_exploration: float = 1.414  # sqrt(2)
    mcts_time_limit: float = 5.0
    
    # Self-training settings
    self_play_games: int = 100
    training_iterations: int = 10
    save_frequency: int = 10
    
    def __post_init__(self):
        if self.nn_hidden_layers is None:
            self.nn_hidden_layers = [512, 256, 128, 64]

@dataclass
class GameConfig:
    """Configuration for game settings"""
    time_control: float = 300.0  # 5 minutes per side
    increment: float = 3.0  # 3 seconds increment
    max_moves: int = 200
    
    # Opening book
    use_opening_book: bool = True
    opening_book_depth: int = 10
    
    # Endgame tablebase
    use_tablebase: bool = True
    tablebase_path: str = "./tablebases"

@dataclass
class LoggingConfig:
    """Configuration for logging and analytics"""
    log_level: str = "INFO"
    save_games: bool = True
    save_analysis: bool = True
    
    # Output directories
    games_dir: str = "./games"
    models_dir: str = "./models"
    logs_dir: str = "./logs"
    analysis_dir: str = "./analysis"
    
    # Analytics settings
    track_metrics: List[str] = None
    
    def __post_init__(self):
        if self.track_metrics is None:
            self.track_metrics = [
                "win_rate",
                "average_game_length",
                "blunder_rate",
                "tactical_accuracy",
                "positional_score",
                "time_usage",
                "move_quality"
            ]
        
        # Create directories
        for directory in [self.games_dir, self.models_dir, self.logs_dir, self.analysis_dir]:
            os.makedirs(directory, exist_ok=True)

@dataclass
class Config:
    """Main configuration class"""
    engine: EngineConfig = None
    ai: AIConfig = None
    game: GameConfig = None
    logging: LoggingConfig = None
    
    # System settings
    use_gpu: bool = True
    num_threads: int = 4
    random_seed: int = 42
    
    def __post_init__(self):
        if self.engine is None:
            self.engine = EngineConfig()
        if self.ai is None:
            self.ai = AIConfig()
        if self.game is None:
            self.game = GameConfig()
        if self.logging is None:
            self.logging = LoggingConfig()

# Global configuration instance
config = Config()
