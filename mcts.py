"""
Monte Carlo Tree Search implementation for chess
Provides multi-step prediction capabilities
"""
import chess
import math
import random
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

from chess_engine import ChessEvaluator, MaterialEvaluator
from config import config

logger = logging.getLogger(__name__)

@dataclass
class MCTSNode:
    """Node in the MCTS tree"""
    board: chess.Board
    move: Optional[chess.Move] = None
    parent: Optional['MCTSNode'] = None
    children: Dict[chess.Move, 'MCTSNode'] = None
    visits: int = 0
    total_score: float = 0.0
    is_terminal: bool = None

    def __post_init__(self):
        if self.children is None:
            self.children = {}
        if self.is_terminal is None:
            self.is_terminal = self.board.is_game_over()
    
    @property
    def average_score(self) -> float:
        """Average score of this node"""
        return self.total_score / self.visits if self.visits > 0 else 0.0
    
    @property
    def ucb1_score(self) -> float:
        """UCB1 score for node selection"""
        if self.visits == 0:
            return float('inf')
        
        if self.parent is None:
            return self.average_score
        
        exploration_term = config.ai.mcts_exploration * math.sqrt(
            math.log(self.parent.visits) / self.visits
        )
        
        return self.average_score + exploration_term
    
    def is_fully_expanded(self) -> bool:
        """Check if all legal moves have been expanded"""
        if self.is_terminal:
            return True
        
        legal_moves = list(self.board.legal_moves)
        return len(self.children) == len(legal_moves)
    
    def select_child(self) -> 'MCTSNode':
        """Select best child using UCB1"""
        return max(self.children.values(), key=lambda child: child.ucb1_score)
    
    def expand(self) -> Optional['MCTSNode']:
        """Expand node by adding a new child"""
        if self.is_terminal:
            return None
        
        legal_moves = list(self.board.legal_moves)
        unexplored_moves = [move for move in legal_moves if move not in self.children]
        
        if not unexplored_moves:
            return None
        
        # Select random unexplored move
        move = random.choice(unexplored_moves)
        
        # Create new board state
        new_board = self.board.copy()
        new_board.push(move)
        
        # Create child node
        child = MCTSNode(
            board=new_board,
            move=move,
            parent=self
        )
        
        self.children[move] = child
        return child
    
    def backpropagate(self, score: float):
        """Backpropagate score up the tree"""
        self.visits += 1
        self.total_score += score
        
        if self.parent:
            # Flip score for opponent
            self.parent.backpropagate(-score)

class MCTSEngine:
    """Monte Carlo Tree Search engine for chess"""
    
    def __init__(self, evaluator: ChessEvaluator):
        self.evaluator = evaluator
        self.root = None
        self.simulations_run = 0
    
    def search(self, board: chess.Board, simulations: Optional[int] = None, 
               time_limit: Optional[float] = None) -> Tuple[chess.Move, float, List[chess.Move]]:
        """
        Search for best move using MCTS
        Returns: (best_move, confidence_score, principal_variation)
        """
        if simulations is None:
            simulations = config.ai.mcts_simulations
        if time_limit is None:
            time_limit = config.ai.mcts_time_limit
        
        self.root = MCTSNode(board=board.copy(), move=None, parent=None)
        self.simulations_run = 0
        
        start_time = time.time()
        
        # Run MCTS simulations
        while (self.simulations_run < simulations and 
               time.time() - start_time < time_limit):
            
            self._run_simulation()
            self.simulations_run += 1
        
        # Select best move
        if not self.root.children:
            # No simulations completed, return random legal move
            legal_moves = list(board.legal_moves)
            return random.choice(legal_moves), 0.0, []
        
        best_child = max(self.root.children.values(), key=lambda child: child.visits)
        best_move = best_child.move
        confidence = best_child.visits / self.simulations_run
        
        # Extract principal variation
        principal_variation = self._extract_pv(best_child)
        
        logger.info(f"MCTS completed {self.simulations_run} simulations")
        logger.info(f"Best move: {best_move}, confidence: {confidence:.3f}")
        
        return best_move, confidence, principal_variation
    
    def _run_simulation(self):
        """Run a single MCTS simulation"""
        # Selection phase
        node = self._select(self.root)
        
        # Expansion phase
        if not node.is_terminal and node.visits > 0:
            expanded_node = node.expand()
            if expanded_node:
                node = expanded_node
        
        # Simulation phase
        score = self._simulate(node)
        
        # Backpropagation phase
        node.backpropagate(score)
    
    def _select(self, node: MCTSNode) -> MCTSNode:
        """Selection phase: traverse tree using UCB1"""
        while not node.is_terminal and node.is_fully_expanded():
            node = node.select_child()
        return node
    
    def _simulate(self, node: MCTSNode) -> float:
        """Simulation phase: random playout or evaluation"""
        board = node.board.copy()
        
        # Use evaluation function instead of full random playout for efficiency
        if board.is_game_over():
            result = board.result()
            if result == "1-0":
                return 1.0
            elif result == "0-1":
                return -1.0
            else:
                return 0.0
        
        # Evaluate position
        score = self.evaluator.evaluate(board)
        
        # Normalize score to [-1, 1] range
        normalized_score = math.tanh(score / 1000.0)
        
        # Adjust for current player
        return normalized_score if board.turn else -normalized_score
    
    def _extract_pv(self, node: MCTSNode, max_depth: int = 10) -> List[chess.Move]:
        """Extract principal variation from MCTS tree"""
        pv = []
        current = node
        depth = 0
        
        while current and current.children and depth < max_depth:
            # Select most visited child
            best_child = max(current.children.values(), key=lambda child: child.visits)
            pv.append(best_child.move)
            current = best_child
            depth += 1
        
        return pv
    
    def get_move_probabilities(self) -> Dict[chess.Move, float]:
        """Get move probabilities based on visit counts"""
        if not self.root or not self.root.children:
            return {}
        
        total_visits = sum(child.visits for child in self.root.children.values())
        
        probabilities = {}
        for move, child in self.root.children.items():
            probabilities[move] = child.visits / total_visits if total_visits > 0 else 0.0
        
        return probabilities
    
    def get_tree_stats(self) -> Dict[str, any]:
        """Get statistics about the MCTS tree"""
        if not self.root:
            return {}
        
        def count_nodes(node: MCTSNode) -> int:
            count = 1
            for child in node.children.values():
                count += count_nodes(child)
            return count
        
        def max_depth(node: MCTSNode, current_depth: int = 0) -> int:
            if not node.children:
                return current_depth
            return max(max_depth(child, current_depth + 1) for child in node.children.values())
        
        return {
            "total_nodes": count_nodes(self.root),
            "max_depth": max_depth(self.root),
            "simulations": self.simulations_run,
            "root_visits": self.root.visits,
            "branching_factor": len(self.root.children)
        }

class MCTSPredictor:
    """Multi-step prediction using MCTS"""
    
    def __init__(self, evaluator: ChessEvaluator):
        self.mcts_engine = MCTSEngine(evaluator)
    
    def predict_sequence(self, board: chess.Board, steps: int = 5) -> List[Tuple[chess.Move, float]]:
        """
        Predict a sequence of moves with confidence scores
        Returns list of (move, confidence) tuples
        """
        sequence = []
        current_board = board.copy()
        
        for step in range(steps):
            if current_board.is_game_over():
                break
            
            # Reduce simulation count for deeper predictions
            simulations = max(100, config.ai.mcts_simulations // (step + 1))
            
            try:
                move, confidence, _ = self.mcts_engine.search(
                    current_board, 
                    simulations=simulations,
                    time_limit=1.0  # Shorter time limit for predictions
                )
                
                sequence.append((move, confidence))
                current_board.push(move)
                
            except Exception as e:
                logger.warning(f"Prediction failed at step {step}: {e}")
                break
        
        return sequence
    
    def analyze_position(self, board: chess.Board) -> Dict[str, any]:
        """Comprehensive position analysis using MCTS"""
        move, confidence, pv = self.mcts_engine.search(board)
        move_probs = self.mcts_engine.get_move_probabilities()
        tree_stats = self.mcts_engine.get_tree_stats()
        
        # Get top moves
        top_moves = sorted(move_probs.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "best_move": move,
            "confidence": confidence,
            "principal_variation": pv,
            "top_moves": top_moves,
            "tree_statistics": tree_stats,
            "position_evaluation": self.mcts_engine.evaluator.evaluate(board)
        }
