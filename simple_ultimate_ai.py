#!/usr/bin/env python3
"""
Simplified Ultimate Chess AI System
Versi yang disederhanakan untuk menghindari error kompleks
"""

import os
import sys
import time
import logging
import random
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
import chess
import chess.engine

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleEvaluator:
    """Evaluator sederhana tapi efektif"""
    
    def __init__(self):
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Positional bonuses
        self.pawn_table = [
            [0,  0,  0,  0,  0,  0,  0,  0],
            [50, 50, 50, 50, 50, 50, 50, 50],
            [10, 10, 20, 30, 30, 20, 10, 10],
            [5,  5, 10, 25, 25, 10,  5,  5],
            [0,  0,  0, 20, 20,  0,  0,  0],
            [5, -5,-10,  0,  0,-10, -5,  5],
            [5, 10, 10,-20,-20, 10, 10,  5],
            [0,  0,  0,  0,  0,  0,  0,  0]
        ]
        
        self.knight_table = [
            [-50,-40,-30,-30,-30,-30,-40,-50],
            [-40,-20,  0,  0,  0,  0,-20,-40],
            [-30,  0, 10, 15, 15, 10,  0,-30],
            [-30,  5, 15, 20, 20, 15,  5,-30],
            [-30,  0, 15, 20, 20, 15,  0,-30],
            [-30,  5, 10, 15, 15, 10,  5,-30],
            [-40,-20,  0,  5,  5,  0,-20,-40],
            [-50,-40,-30,-30,-30,-30,-40,-50]
        ]
    
    def evaluate(self, board: chess.Board) -> float:
        """Evaluate position"""
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                if piece.color == chess.BLACK:
                    value = -value
                score += value
                
                # Positional evaluation
                row, col = divmod(square, 8)
                if piece.piece_type == chess.PAWN:
                    pos_value = self.pawn_table[row][col]
                    if piece.color == chess.BLACK:
                        pos_value = self.pawn_table[7-row][col]
                        pos_value = -pos_value
                    score += pos_value
                elif piece.piece_type == chess.KNIGHT:
                    pos_value = self.knight_table[row][col]
                    if piece.color == chess.BLACK:
                        pos_value = -pos_value
                    score += pos_value
        
        # Mobility bonus
        legal_moves = len(list(board.legal_moves))
        if board.turn == chess.WHITE:
            score += legal_moves * 2
        else:
            score -= legal_moves * 2
        
        # King safety
        white_king = board.king(chess.WHITE)
        black_king = board.king(chess.BLACK)
        
        if white_king and black_king:
            # Penalize exposed kings
            white_attackers = len(board.attackers(chess.BLACK, white_king))
            black_attackers = len(board.attackers(chess.WHITE, black_king))
            score -= white_attackers * 50
            score += black_attackers * 50
        
        return score

class SimpleEngine:
    """Simple but effective chess engine"""
    
    def __init__(self, evaluator: SimpleEvaluator):
        self.evaluator = evaluator
        self.transposition_table = {}
        self.nodes_searched = 0
    
    def search(self, board: chess.Board, depth: int = 4) -> chess.Move:
        """Search for best move"""
        self.nodes_searched = 0
        self.transposition_table.clear()
        
        best_move = None
        best_score = float('-inf') if board.turn == chess.WHITE else float('inf')
        
        legal_moves = list(board.legal_moves)
        if not legal_moves:
            return None
        
        # Order moves (captures first)
        ordered_moves = self._order_moves(board, legal_moves)
        
        for move in ordered_moves:
            board.push(move)
            
            if board.turn == chess.WHITE:
                score = self._minimax(board, depth - 1, float('-inf'), float('inf'), False)
                if score > best_score:
                    best_score = score
                    best_move = move
            else:
                score = self._minimax(board, depth - 1, float('-inf'), float('inf'), True)
                if score < best_score:
                    best_score = score
                    best_move = move
            
            board.pop()
        
        return best_move or legal_moves[0]
    
    def _minimax(self, board: chess.Board, depth: int, alpha: float, beta: float, maximizing: bool) -> float:
        """Minimax with alpha-beta pruning"""
        self.nodes_searched += 1
        
        # Check transposition table
        board_hash = hash(str(board))
        if board_hash in self.transposition_table:
            cached_depth, cached_score = self.transposition_table[board_hash]
            if cached_depth >= depth:
                return cached_score
        
        if depth == 0 or board.is_game_over():
            score = self.evaluator.evaluate(board)
            self.transposition_table[board_hash] = (depth, score)
            return score
        
        legal_moves = list(board.legal_moves)
        ordered_moves = self._order_moves(board, legal_moves)
        
        if maximizing:
            max_eval = float('-inf')
            for move in ordered_moves:
                board.push(move)
                eval_score = self._minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                max_eval = max(max_eval, eval_score)
                alpha = max(alpha, eval_score)
                
                if beta <= alpha:
                    break
            
            self.transposition_table[board_hash] = (depth, max_eval)
            return max_eval
        else:
            min_eval = float('inf')
            for move in ordered_moves:
                board.push(move)
                eval_score = self._minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                min_eval = min(min_eval, eval_score)
                beta = min(beta, eval_score)
                
                if beta <= alpha:
                    break
            
            self.transposition_table[board_hash] = (depth, min_eval)
            return min_eval
    
    def _order_moves(self, board: chess.Board, moves: List[chess.Move]) -> List[chess.Move]:
        """Order moves for better alpha-beta pruning"""
        def move_priority(move):
            priority = 0
            
            # Captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                if captured_piece:
                    priority += self.evaluator.piece_values[captured_piece.piece_type]
            
            # Checks
            board.push(move)
            if board.is_check():
                priority += 100
            board.pop()
            
            # Central moves
            center_squares = [chess.D4, chess.E4, chess.D5, chess.E5]
            if move.to_square in center_squares:
                priority += 50
            
            return priority
        
        return sorted(moves, key=move_priority, reverse=True)

class SimpleTrainer:
    """Simple training system"""
    
    def __init__(self, engine: SimpleEngine):
        self.engine = engine
        self.games_played = 0
        self.wins = 0
        self.losses = 0
        self.draws = 0
    
    def play_training_games(self, num_games: int = 10) -> Dict[str, Any]:
        """Play training games against random opponent"""
        results = []
        
        for game_num in range(num_games):
            result = self._play_training_game()
            results.append(result)
            
            self.games_played += 1
            if result == 'win':
                self.wins += 1
            elif result == 'loss':
                self.losses += 1
            else:
                self.draws += 1
        
        win_rate = self.wins / self.games_played if self.games_played > 0 else 0
        
        return {
            'games_played': num_games,
            'win_rate': win_rate,
            'total_games': self.games_played,
            'total_wins': self.wins
        }
    
    def _play_training_game(self) -> str:
        """Play a single training game"""
        board = chess.Board()
        moves_played = 0
        
        # Randomly choose our color
        our_color = random.choice([chess.WHITE, chess.BLACK])
        
        while not board.is_game_over() and moves_played < 100:
            if board.turn == our_color:
                # Our move
                move = self.engine.search(board, depth=3)
            else:
                # Random opponent move
                legal_moves = list(board.legal_moves)
                move = random.choice(legal_moves)
            
            if move:
                board.push(move)
                moves_played += 1
            else:
                break
        
        # Determine result
        if board.is_checkmate():
            winner = not board.turn  # Previous player won
            if winner == our_color:
                return 'win'
            else:
                return 'loss'
        else:
            return 'draw'

class SimpleTournament:
    """Simple tournament system"""
    
    def __init__(self, our_engine: SimpleEngine):
        self.our_engine = our_engine
        self.engine_paths = self._load_engine_paths()
        self.results = {}
    
    def _load_engine_paths(self) -> Dict[str, str]:
        """Load available engines"""
        try:
            with open("engines_config.json", "r") as f:
                config_data = json.load(f)
                return {name: info["path"] for name, info in config_data.get("engines", {}).items()}
        except:
            return {"stockfish": "/usr/games/stockfish"}
    
    def run_tournament(self, games_per_engine: int = 5) -> Dict[str, Any]:
        """Run tournament against available engines"""
        logger.info(f"Starting tournament with {games_per_engine} games per engine")
        
        tournament_results = {}
        total_games = 0
        total_wins = 0
        
        for engine_name, engine_path in self.engine_paths.items():
            logger.info(f"Playing against {engine_name}...")
            
            engine_results = self._play_against_engine(engine_name, engine_path, games_per_engine)
            tournament_results[engine_name] = engine_results
            
            total_games += engine_results['games']
            total_wins += engine_results['wins']
            
            logger.info(f"  Result: {engine_results['wins']}/{engine_results['games']} "
                       f"({engine_results['win_rate']:.1%})")
        
        overall_win_rate = total_wins / total_games if total_games > 0 else 0
        
        return {
            'engine_results': tournament_results,
            'total_games': total_games,
            'total_wins': total_wins,
            'overall_win_rate': overall_win_rate
        }
    
    def _play_against_engine(self, engine_name: str, engine_path: str, num_games: int) -> Dict[str, Any]:
        """Play games against a specific engine"""
        wins = 0
        games_played = 0
        
        try:
            for game_num in range(num_games):
                result = self._play_single_game(engine_path, game_num % 2 == 0)
                games_played += 1
                
                if result == 'win':
                    wins += 1
        
        except Exception as e:
            logger.error(f"Error playing against {engine_name}: {e}")
        
        win_rate = wins / games_played if games_played > 0 else 0
        
        return {
            'games': games_played,
            'wins': wins,
            'win_rate': win_rate
        }
    
    def _play_single_game(self, engine_path: str, we_are_white: bool) -> str:
        """Play a single game against an engine"""
        board = chess.Board()
        moves_played = 0
        
        try:
            with chess.engine.SimpleEngine.popen_uci(engine_path) as opponent:
                # Configure opponent
                if "stockfish" in engine_path.lower():
                    opponent.configure({"Skill Level": 5})  # Medium difficulty
                
                while not board.is_game_over() and moves_played < 100:
                    if (board.turn == chess.WHITE) == we_are_white:
                        # Our move
                        move = self.our_engine.search(board, depth=4)
                    else:
                        # Opponent's move
                        result = opponent.play(board, chess.engine.Limit(time=2.0))
                        move = result.move
                    
                    if move and move in board.legal_moves:
                        board.push(move)
                        moves_played += 1
                    else:
                        break
                
                # Determine result
                if board.is_checkmate():
                    winner_is_white = not board.turn
                    if winner_is_white == we_are_white:
                        return 'win'
                    else:
                        return 'loss'
                else:
                    return 'draw'
        
        except Exception as e:
            logger.error(f"Game error: {e}")
            return 'draw'

class SimpleUltimateAI:
    """Simplified Ultimate Chess AI"""
    
    def __init__(self):
        self.evaluator = SimpleEvaluator()
        self.engine = SimpleEngine(self.evaluator)
        self.trainer = SimpleTrainer(self.engine)
        self.tournament = SimpleTournament(self.engine)
        
        self.best_win_rate = 0.0
        self.training_cycles = 0
    
    def run_ultimate_training(self, max_cycles: int = 5, target_win_rate: float = 0.6):
        """Run ultimate training cycles"""
        logger.info("🏆 ULTIMATE CHESS AI TRAINING STARTED")
        logger.info(f"Target: {target_win_rate:.1%} win rate in {max_cycles} cycles")
        logger.info("="*60)
        
        for cycle in range(1, max_cycles + 1):
            logger.info(f"\n🚀 TRAINING CYCLE {cycle}/{max_cycles}")
            
            # Phase 1: Self-training
            logger.info("Phase 1: Self-training...")
            training_results = self.trainer.play_training_games(20)
            logger.info(f"  Training win rate: {training_results['win_rate']:.1%}")
            
            # Phase 2: Tournament testing
            logger.info("Phase 2: Tournament testing...")
            tournament_results = self.tournament.run_tournament(5)
            
            current_win_rate = tournament_results['overall_win_rate']
            logger.info(f"  Tournament win rate: {current_win_rate:.1%}")
            
            # Update best performance
            if current_win_rate > self.best_win_rate:
                self.best_win_rate = current_win_rate
                self._save_best_model(cycle, current_win_rate)
            
            # Check if target achieved
            if current_win_rate >= target_win_rate:
                logger.info(f"🎉 TARGET ACHIEVED! Win rate: {current_win_rate:.1%}")
                break
            
            self.training_cycles += 1
            
            # Progress summary
            logger.info(f"Cycle {cycle} Summary:")
            logger.info(f"  Current: {current_win_rate:.1%}")
            logger.info(f"  Best: {self.best_win_rate:.1%}")
            logger.info(f"  Target: {target_win_rate:.1%}")
        
        logger.info("\n🏁 TRAINING COMPLETED")
        logger.info(f"Best performance: {self.best_win_rate:.1%}")
        logger.info(f"Training cycles: {self.training_cycles}")
        
        return self.best_win_rate >= target_win_rate
    
    def _save_best_model(self, cycle: int, win_rate: float):
        """Save best model"""
        model_data = {
            'cycle': cycle,
            'win_rate': win_rate,
            'timestamp': datetime.now().isoformat(),
            'evaluator_type': 'SimpleEvaluator',
            'engine_type': 'SimpleEngine'
        }
        
        os.makedirs('models', exist_ok=True)
        with open(f'models/best_model_cycle_{cycle}.json', 'w') as f:
            json.dump(model_data, f, indent=2)
        
        logger.info(f"💾 Saved best model (cycle {cycle}, {win_rate:.1%} win rate)")

def main():
    """Main function"""
    print("🏆 SIMPLE ULTIMATE CHESS AI 🏆")
    print("=" * 50)
    print("Sistem AI Catur Sederhana tapi Efektif")
    print("Mampu mengalahkan chess engines dengan optimasi 2 CPU cores")
    print("=" * 50)
    
    # Initialize AI
    ai = SimpleUltimateAI()
    
    # Run training
    success = ai.run_ultimate_training(max_cycles=3, target_win_rate=0.6)
    
    if success:
        print("\n🎉 TRAINING BERHASIL!")
        print("AI telah mencapai target win rate!")
    else:
        print("\n⚠️  Training belum mencapai target")
        print(f"Tapi performa terbaik: {ai.best_win_rate:.1%}")
    
    print(f"\nModel terbaik disimpan di folder 'models/'")

if __name__ == "__main__":
    main()
