#!/usr/bin/env python3
"""
Comprehensive benchmarking script for Advanced Chess AI
Tests against multiple engines and generates detailed reports
"""
import argparse
import json
import os
import time
import logging
from typing import Dict, List, Any
from datetime import datetime

from main import ChessAI
from uci_interface import EngineManager
from analytics import ChessAnalyzer
from config import config
import chess

logger = logging.getLogger(__name__)

class BenchmarkSuite:
    """Comprehensive benchmarking suite"""
    
    def __init__(self):
        self.ai = ChessAI()
        self.engine_manager = EngineManager()
        self.analyzer = ChessAnalyzer()
        self.results = {}
        
    def run_engine_benchmark(self, engine_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Run benchmark against multiple engines"""
        print("=== Engine Benchmark Suite ===")
        
        all_results = {}
        
        for engine_config in engine_configs:
            engine_name = engine_config["name"]
            engine_path = engine_config["path"]
            num_games = engine_config.get("games", 10)
            time_control = engine_config.get("time_control", 5.0)
            
            print(f"\n--- Testing against {engine_name} ---")
            print(f"Games: {num_games}, Time control: {time_control}s")
            
            engine_results = []
            
            for game_num in range(num_games):
                print(f"Game {game_num + 1}/{num_games}", end=" ")
                
                # Alternate colors
                our_color = chess.WHITE if game_num % 2 == 0 else chess.BLACK
                
                try:
                    result = self.engine_manager.play_against_engine(
                        engine_path, time_control, our_color
                    )
                    engine_results.append(result)
                    print(f"- {result.result}")
                    
                except Exception as e:
                    print(f"- ERROR: {e}")
                    logger.error(f"Game {game_num + 1} failed: {e}")
                    continue
            
            # Calculate statistics for this engine
            if engine_results:
                stats = self._calculate_engine_stats(engine_results, engine_name)
                all_results[engine_name] = {
                    "config": engine_config,
                    "games": engine_results,
                    "statistics": stats
                }
                
                print(f"\nResults vs {engine_name}:")
                print(f"  Win rate: {stats['win_rate']:.3f}")
                print(f"  Games: {stats['total_games']}")
                print(f"  W/L/D: {stats['wins']}/{stats['losses']}/{stats['draws']}")
        
        return all_results
    
    def _calculate_engine_stats(self, games: List, engine_name: str) -> Dict[str, Any]:
        """Calculate statistics for games against an engine"""
        total_games = len(games)
        
        wins = sum(1 for game in games if 
                  (game.white_player == "Our AI" and game.result == "1-0") or
                  (game.black_player == "Our AI" and game.result == "0-1"))
        
        losses = sum(1 for game in games if 
                    (game.white_player == "Our AI" and game.result == "0-1") or
                    (game.black_player == "Our AI" and game.result == "1-0"))
        
        draws = sum(1 for game in games if game.result == "1/2-1/2")
        
        win_rate = wins / total_games if total_games > 0 else 0
        avg_game_length = sum(game.game_length for game in games) / total_games
        
        return {
            "engine_name": engine_name,
            "total_games": total_games,
            "wins": wins,
            "losses": losses,
            "draws": draws,
            "win_rate": win_rate,
            "average_game_length": avg_game_length,
            "performance_rating": self._estimate_elo(wins, losses, draws)
        }
    
    def _estimate_elo(self, wins: int, losses: int, draws: int) -> float:
        """Estimate Elo rating based on results"""
        total = wins + losses + draws
        if total == 0:
            return 1200
        
        score = (wins + 0.5 * draws) / total
        
        if score >= 1.0:
            return 2000
        elif score <= 0.0:
            return 800
        else:
            # Simple Elo calculation assuming opponent is 1500
            elo_diff = -400 * (score - 0.5) / 0.5
            return 1500 + elo_diff
    
    def run_self_play_benchmark(self, iterations: int = 5, games_per_iteration: int = 20) -> Dict[str, Any]:
        """Run self-play training benchmark"""
        print(f"\n=== Self-Play Training Benchmark ===")
        print(f"Iterations: {iterations}, Games per iteration: {games_per_iteration}")
        
        training_results = []
        
        for iteration in range(iterations):
            print(f"\nIteration {iteration + 1}/{iterations}")
            
            start_time = time.time()
            result = self.ai.trainer.run_training_iteration(games_per_iteration)
            iteration_time = time.time() - start_time
            
            result["iteration_time"] = iteration_time
            training_results.append(result)
            
            print(f"  Loss: {result['training_loss']:.6f}")
            print(f"  Win rate: {result['win_rate']:.3f}")
            print(f"  Time: {iteration_time:.1f}s")
        
        return {
            "training_results": training_results,
            "total_iterations": iterations,
            "games_per_iteration": games_per_iteration,
            "improvement_trend": self._analyze_training_trend(training_results)
        }
    
    def _analyze_training_trend(self, results: List[Dict]) -> Dict[str, Any]:
        """Analyze training improvement trend"""
        if len(results) < 2:
            return {}
        
        losses = [r["training_loss"] for r in results]
        win_rates = [r["win_rate"] for r in results]
        
        # Calculate trends
        loss_trend = "improving" if losses[-1] < losses[0] else "degrading"
        win_rate_trend = "improving" if win_rates[-1] > win_rates[0] else "degrading"
        
        return {
            "loss_trend": loss_trend,
            "win_rate_trend": win_rate_trend,
            "loss_improvement": losses[0] - losses[-1],
            "win_rate_improvement": win_rates[-1] - win_rates[0],
            "convergence": "yes" if abs(losses[-1] - losses[-2]) < 0.001 else "no"
        }
    
    def run_performance_benchmark(self) -> Dict[str, Any]:
        """Run performance and speed benchmarks"""
        print("\n=== Performance Benchmark ===")
        
        # Test different search depths
        depth_results = {}
        test_position = chess.Board()
        
        for depth in [2, 3, 4, 5]:
            print(f"Testing depth {depth}...")
            
            start_time = time.time()
            result = self.ai.minimax_engine.search(test_position, depth)
            search_time = time.time() - start_time
            
            depth_results[depth] = {
                "time": search_time,
                "nodes": self.ai.minimax_engine.nodes_searched,
                "nps": self.ai.minimax_engine.nodes_searched / search_time if search_time > 0 else 0,
                "best_move": str(result.move),
                "score": result.score
            }
            
            print(f"  Time: {search_time:.3f}s, Nodes: {self.ai.minimax_engine.nodes_searched}, NPS: {depth_results[depth]['nps']:.0f}")
        
        # Test MCTS performance
        print("Testing MCTS...")
        mcts_results = {}
        
        for simulations in [100, 500, 1000, 2000]:
            start_time = time.time()
            move, confidence, pv = self.ai.mcts_engine.search(test_position, simulations=simulations)
            search_time = time.time() - start_time
            
            mcts_results[simulations] = {
                "time": search_time,
                "simulations": simulations,
                "sps": simulations / search_time if search_time > 0 else 0,
                "confidence": confidence,
                "best_move": str(move)
            }
            
            print(f"  Simulations: {simulations}, Time: {search_time:.3f}s, SPS: {mcts_results[simulations]['sps']:.0f}")
        
        return {
            "minimax_performance": depth_results,
            "mcts_performance": mcts_results,
            "system_info": self._get_system_info()
        }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        import platform
        import psutil
        
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_gb": psutil.virtual_memory().total / (1024**3),
            "gpu_available": self._check_gpu()
        }
    
    def _check_gpu(self) -> bool:
        """Check if GPU is available"""
        try:
            import torch
            return torch.cuda.is_available()
        except:
            return False
    
    def generate_report(self, results: Dict[str, Any], output_file: str = None) -> str:
        """Generate comprehensive benchmark report"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"benchmark_report_{timestamp}.json"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": results,
            "summary": self._generate_summary(results),
            "recommendations": self._generate_recommendations(results)
        }
        
        output_path = os.path.join(config.logging.analysis_dir, output_file)
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nBenchmark report saved to: {output_path}")
        return output_path
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of benchmark results"""
        summary = {}
        
        if "engine_benchmark" in results:
            engine_results = results["engine_benchmark"]
            total_games = sum(r["statistics"]["total_games"] for r in engine_results.values())
            total_wins = sum(r["statistics"]["wins"] for r in engine_results.values())
            overall_win_rate = total_wins / total_games if total_games > 0 else 0
            
            summary["engine_performance"] = {
                "total_games": total_games,
                "overall_win_rate": overall_win_rate,
                "engines_tested": len(engine_results)
            }
        
        if "self_play_benchmark" in results:
            training = results["self_play_benchmark"]
            summary["training_performance"] = {
                "iterations_completed": training["total_iterations"],
                "total_games": training["total_iterations"] * training["games_per_iteration"],
                "improvement_trend": training["improvement_trend"]
            }
        
        return summary
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on results"""
        recommendations = []
        
        if "engine_benchmark" in results:
            engine_results = results["engine_benchmark"]
            avg_win_rate = sum(r["statistics"]["win_rate"] for r in engine_results.values()) / len(engine_results)
            
            if avg_win_rate < 0.3:
                recommendations.append("Consider increasing search depth or MCTS simulations")
                recommendations.append("Review evaluation function weights")
            elif avg_win_rate > 0.7:
                recommendations.append("AI is performing well - consider testing against stronger opponents")
        
        if "performance_benchmark" in results:
            perf = results["performance_benchmark"]
            if "minimax_performance" in perf:
                depth_4_time = perf["minimax_performance"].get(4, {}).get("time", 0)
                if depth_4_time > 10:
                    recommendations.append("Search is slow - consider optimizing evaluation function")
        
        return recommendations

def main():
    """Main benchmark function"""
    parser = argparse.ArgumentParser(description="Chess AI Benchmark Suite")
    parser.add_argument("--engines", action="store_true", help="Run engine benchmarks")
    parser.add_argument("--training", action="store_true", help="Run training benchmarks")
    parser.add_argument("--performance", action="store_true", help="Run performance benchmarks")
    parser.add_argument("--all", action="store_true", help="Run all benchmarks")
    parser.add_argument("--games", type=int, default=10, help="Games per engine")
    parser.add_argument("--iterations", type=int, default=3, help="Training iterations")
    parser.add_argument("--output", type=str, help="Output file name")
    
    args = parser.parse_args()
    
    if not any([args.engines, args.training, args.performance, args.all]):
        print("Please specify at least one benchmark type")
        parser.print_help()
        return
    
    benchmark = BenchmarkSuite()
    results = {}
    
    # Engine benchmarks
    if args.engines or args.all:
        engine_configs = [
            {
                "name": "Stockfish_Level_1",
                "path": "/usr/games/stockfish",
                "games": args.games,
                "time_control": 2.0
            },
            {
                "name": "Stockfish_Level_5", 
                "path": "/usr/games/stockfish",
                "games": args.games,
                "time_control": 5.0
            }
        ]
        
        results["engine_benchmark"] = benchmark.run_engine_benchmark(engine_configs)
    
    # Training benchmarks
    if args.training or args.all:
        results["self_play_benchmark"] = benchmark.run_self_play_benchmark(
            iterations=args.iterations,
            games_per_iteration=10
        )
    
    # Performance benchmarks
    if args.performance or args.all:
        results["performance_benchmark"] = benchmark.run_performance_benchmark()
    
    # Generate report
    report_file = benchmark.generate_report(results, args.output)
    print(f"\nBenchmark completed! Report: {report_file}")

if __name__ == "__main__":
    main()
