#!/usr/bin/env python3
"""
Script untuk menginstall semua chess engine yang diperlukan
"""

import os
import subprocess
import urllib.request
import zipfile
import tarfile
import shutil
import platform
import logging
from pathlib import Path
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

class ChessEngineInstaller:
    """Installer untuk berbagai chess engine"""
    
    def __init__(self):
        self.engines_dir = Path("engines")
        self.engines_dir.mkdir(exist_ok=True)
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        
        # Engine configurations
        self.engines = {
            "stockfish": {
                "name": "Stockfish",
                "cpu_only": True,
                "strength": "Terkuat",
                "urls": {
                    "linux": "https://github.com/official-stockfish/Stockfish/releases/download/sf_16/stockfish-ubuntu-x86-64-avx2.tar",
                    "windows": "https://github.com/official-stockfish/Stockfish/releases/download/sf_16/stockfish-windows-x86-64-avx2.zip",
                    "darwin": "https://github.com/official-stockfish/Stockfish/releases/download/sf_16/stockfish-macos-x86-64-avx2.tar"
                }
            },
            "komodo": {
                "name": "Komodo",
                "cpu_only": True,
                "strength": "Sangat Kuat",
                "note": "Versi gratis terbatas"
            },
            "ethereal": {
                "name": "Ethereal",
                "cpu_only": True,
                "strength": "Kuat",
                "urls": {
                    "linux": "https://github.com/AndyGrant/Ethereal/releases/download/v14.25/Ethereal-14.25-x64-linux.tar.gz",
                    "windows": "https://github.com/AndyGrant/Ethereal/releases/download/v14.25/Ethereal-14.25-x64-windows.zip"
                }
            },
            "berserk": {
                "name": "Berserk",
                "cpu_only": True,
                "strength": "Kuat",
                "urls": {
                    "linux": "https://github.com/jhonnold/berserk/releases/download/11.1/berserk-11.1-x86_64-linux.tar.gz",
                    "windows": "https://github.com/jhonnold/berserk/releases/download/11.1/berserk-11.1-x86_64-windows.zip"
                }
            },
            "rubichess": {
                "name": "RubiChess",
                "cpu_only": True,
                "strength": "Menengah-Kuat",
                "urls": {
                    "linux": "https://github.com/Matthies/RubiChess/releases/download/20240112/rubichess-20240112-linux.tar.gz",
                    "windows": "https://github.com/Matthies/RubiChess/releases/download/20240112/rubichess-20240112-win64.zip"
                }
            },
            "minic": {
                "name": "Minic",
                "cpu_only": True,
                "strength": "Ringan",
                "urls": {
                    "linux": "https://github.com/tryingsomestuff/Minic/releases/download/v3.41/minic_3.41_linux_x64.tar.gz",
                    "windows": "https://github.com/tryingsomestuff/Minic/releases/download/v3.41/minic_3.41_win64.zip"
                }
            }
        }
        
        self.installed_engines = {}
    
    def install_all_engines(self) -> Dict[str, str]:
        """Install semua engine yang tersedia"""
        print("=== Installing Chess Engines ===")
        print(f"System: {self.system}, Architecture: {self.arch}")
        print()
        
        results = {}
        
        for engine_id, engine_info in self.engines.items():
            print(f"Installing {engine_info['name']}...")
            try:
                path = self.install_engine(engine_id)
                if path:
                    results[engine_id] = path
                    print(f"✓ {engine_info['name']} installed successfully")
                else:
                    print(f"✗ Failed to install {engine_info['name']}")
            except Exception as e:
                print(f"✗ Error installing {engine_info['name']}: {e}")
                logger.error(f"Failed to install {engine_id}: {e}")
        
        # Try to install via package manager as fallback
        self._install_via_package_manager()
        
        # Verify installations
        verified_engines = self.verify_installations(results)
        
        print(f"\n=== Installation Summary ===")
        print(f"Successfully installed: {len(verified_engines)} engines")
        for engine_id, path in verified_engines.items():
            engine_name = self.engines[engine_id]["name"]
            print(f"  {engine_name}: {path}")
        
        return verified_engines
    
    def install_engine(self, engine_id: str) -> str:
        """Install specific engine"""
        engine_info = self.engines.get(engine_id)
        if not engine_info:
            raise ValueError(f"Unknown engine: {engine_id}")
        
        # Check if already installed
        existing_path = self._find_existing_engine(engine_id)
        if existing_path:
            return existing_path
        
        # Download and install
        if "urls" in engine_info:
            return self._download_and_install(engine_id, engine_info)
        else:
            return self._install_from_source_or_package(engine_id, engine_info)
    
    def _find_existing_engine(self, engine_id: str) -> str:
        """Check if engine is already installed"""
        # Check in engines directory
        engine_dir = self.engines_dir / engine_id
        if engine_dir.exists():
            for file in engine_dir.iterdir():
                if file.is_file() and file.stat().st_mode & 0o111:  # Executable
                    return str(file)
        
        # Check system PATH
        try:
            result = subprocess.run(['which', engine_id], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        return None
    
    def _download_and_install(self, engine_id: str, engine_info: Dict) -> str:
        """Download and install engine from URL"""
        urls = engine_info.get("urls", {})
        url = urls.get(self.system)
        
        if not url:
            raise ValueError(f"No download URL for {self.system}")
        
        # Create engine directory
        engine_dir = self.engines_dir / engine_id
        engine_dir.mkdir(exist_ok=True)
        
        # Download file
        filename = url.split("/")[-1]
        download_path = engine_dir / filename
        
        print(f"  Downloading {filename}...")
        urllib.request.urlretrieve(url, download_path)
        
        # Extract archive
        extracted_path = self._extract_archive(download_path, engine_dir)
        
        # Find executable
        executable_path = self._find_executable(engine_dir, engine_id)
        
        if executable_path:
            # Make executable
            os.chmod(executable_path, 0o755)
            return executable_path
        
        raise RuntimeError(f"Could not find executable for {engine_id}")
    
    def _extract_archive(self, archive_path: Path, extract_dir: Path) -> Path:
        """Extract archive file"""
        if archive_path.suffix == '.zip':
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
        elif archive_path.suffix in ['.tar', '.gz']:
            with tarfile.open(archive_path, 'r:*') as tar_ref:
                tar_ref.extractall(extract_dir)
        else:
            raise ValueError(f"Unsupported archive format: {archive_path.suffix}")
        
        # Remove archive file
        archive_path.unlink()
        
        return extract_dir
    
    def _find_executable(self, search_dir: Path, engine_name: str) -> str:
        """Find executable file in directory"""
        # Common executable names
        possible_names = [
            engine_name,
            engine_name.lower(),
            engine_name.capitalize(),
            f"{engine_name}.exe",
            f"{engine_name.lower()}.exe"
        ]
        
        # Search recursively
        for file_path in search_dir.rglob("*"):
            if file_path.is_file():
                # Check if filename matches
                if file_path.name.lower() in [name.lower() for name in possible_names]:
                    return str(file_path)
                
                # Check if file is executable and contains engine name
                if (file_path.stat().st_mode & 0o111 and 
                    engine_name.lower() in file_path.name.lower()):
                    return str(file_path)
        
        return None
    
    def _install_from_source_or_package(self, engine_id: str, engine_info: Dict) -> str:
        """Install engine from source or package manager"""
        if engine_id == "komodo":
            return self._install_komodo()
        elif engine_id == "fairy_stockfish":
            return self._install_fairy_stockfish()
        elif engine_id == "winter":
            return self._install_winter()
        
        return None
    
    def _install_komodo(self) -> str:
        """Install Komodo (free version if available)"""
        # Komodo free version is limited, try to find it
        print("  Komodo requires manual installation (commercial engine)")
        return None
    
    def _install_fairy_stockfish(self) -> str:
        """Install Fairy-Stockfish"""
        try:
            # Try to compile from source
            repo_url = "https://github.com/ianfab/Fairy-Stockfish.git"
            engine_dir = self.engines_dir / "fairy_stockfish"
            
            if not engine_dir.exists():
                subprocess.run(['git', 'clone', repo_url, str(engine_dir)], check=True)
            
            # Compile
            subprocess.run(['make', '-C', str(engine_dir), 'build'], check=True)
            
            executable = engine_dir / "fairy-stockfish"
            if executable.exists():
                return str(executable)
        except:
            pass
        
        return None
    
    def _install_winter(self) -> str:
        """Install Winter engine"""
        try:
            # Try to compile from source
            repo_url = "https://github.com/rosenthj/Winter.git"
            engine_dir = self.engines_dir / "winter"
            
            if not engine_dir.exists():
                subprocess.run(['git', 'clone', repo_url, str(engine_dir)], check=True)
            
            # Compile
            subprocess.run(['make', '-C', str(engine_dir)], check=True)
            
            executable = engine_dir / "winter"
            if executable.exists():
                return str(executable)
        except:
            pass
        
        return None
    
    def _install_via_package_manager(self):
        """Try to install engines via package manager"""
        if self.system == "linux":
            try:
                # Try to install Stockfish via apt
                subprocess.run(['sudo', 'apt-get', 'update'], check=False)
                subprocess.run(['sudo', 'apt-get', 'install', '-y', 'stockfish'], check=False)
                print("✓ Attempted to install Stockfish via apt")
            except:
                pass
        
        elif self.system == "darwin":
            try:
                # Try to install via Homebrew
                subprocess.run(['brew', 'install', 'stockfish'], check=False)
                print("✓ Attempted to install Stockfish via Homebrew")
            except:
                pass
    
    def verify_installations(self, engine_paths: Dict[str, str]) -> Dict[str, str]:
        """Verify that installed engines work"""
        verified = {}
        
        for engine_id, path in engine_paths.items():
            if self._test_engine(path):
                verified[engine_id] = path
            else:
                print(f"⚠ {engine_id} installed but not working properly")
        
        # Also check system-installed engines
        system_engines = ['stockfish', 'gnuchess']
        for engine in system_engines:
            try:
                result = subprocess.run(['which', engine], capture_output=True, text=True)
                if result.returncode == 0:
                    path = result.stdout.strip()
                    if self._test_engine(path):
                        verified[engine] = path
            except:
                pass
        
        return verified
    
    def _test_engine(self, engine_path: str) -> bool:
        """Test if engine responds to UCI commands"""
        try:
            process = subprocess.Popen(
                [engine_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Send UCI command
            stdout, stderr = process.communicate(input="uci\nquit\n", timeout=5)
            
            # Check if engine responds with uciok
            return "uciok" in stdout.lower()
        
        except Exception as e:
            logger.debug(f"Engine test failed for {engine_path}: {e}")
            return False
    
    def create_engine_config(self, verified_engines: Dict[str, str]):
        """Create configuration file for installed engines"""
        config_data = {
            "engines": {},
            "installation_info": {
                "system": self.system,
                "architecture": self.arch,
                "engines_directory": str(self.engines_dir)
            }
        }
        
        for engine_id, path in verified_engines.items():
            engine_info = self.engines.get(engine_id, {})
            config_data["engines"][engine_id] = {
                "name": engine_info.get("name", engine_id),
                "path": path,
                "strength": engine_info.get("strength", "Unknown"),
                "cpu_only": engine_info.get("cpu_only", True)
            }
        
        # Save configuration
        import json
        with open("engines_config.json", "w") as f:
            json.dump(config_data, f, indent=2)
        
        print(f"\n✓ Engine configuration saved to engines_config.json")

def main():
    """Main installation function"""
    installer = ChessEngineInstaller()
    
    print("Chess Engine Installer")
    print("======================")
    print("This will install multiple chess engines for training and benchmarking.")
    print()
    
    # Install all engines
    verified_engines = installer.install_all_engines()
    
    if verified_engines:
        # Create configuration
        installer.create_engine_config(verified_engines)
        
        print(f"\n🎉 Installation completed!")
        print(f"Installed {len(verified_engines)} engines successfully.")
        print("\nYou can now use these engines for training and benchmarking.")
    else:
        print("\n❌ No engines were successfully installed.")
        print("Please check the error messages above and try manual installation.")

if __name__ == "__main__":
    main()
