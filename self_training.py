"""
Self-training system for chess AI
Implements self-play and learning mechanisms
"""
import chess
import chess.pgn
import numpy as np
import json
import os
import time
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass, asdict
import logging
from tqdm import tqdm

# Optional PyTorch imports
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available, self-training features will be limited")

from chess_engine import NeuralEvaluator, MaterialEvaluator, HybridEvaluator, MoveEvaluation
from mcts import MCTSEngine, MCTSPredictor
from config import config

logger = logging.getLogger(__name__)

@dataclass
class TrainingGame:
    """Container for a training game"""
    moves: List[chess.Move]
    positions: List[str]  # FEN strings
    evaluations: List[float]
    result: str  # "1-0", "0-1", "1/2-1/2"
    game_length: int
    timestamp: float

@dataclass
class TrainingData:
    """Training data point"""
    position: str  # FEN
    evaluation: float
    result: float  # Game result from this position's perspective
    move_number: int

if TORCH_AVAILABLE:
    class ChessDataset(Dataset):
        """PyTorch dataset for chess positions"""

        def __init__(self, training_data: List[TrainingData]):
            self.data = training_data
            self.neural_evaluator = NeuralEvaluator()

        def __len__(self):
            return len(self.data)

        def __getitem__(self, idx):
            item = self.data[idx]
            board = chess.Board(item.position)
            features = self.neural_evaluator._board_to_features(board)

            # Target is combination of evaluation and game result
            target = 0.7 * item.evaluation + 0.3 * item.result
            target = np.tanh(target / 1000.0)  # Normalize to [-1, 1]

            return torch.FloatTensor(features), torch.FloatTensor([target])
else:
    class ChessDataset:
        """Dummy dataset when PyTorch is not available"""
        def __init__(self, training_data):
            self.data = training_data

class SelfTrainer:
    """Self-training system for chess AI"""
    
    def __init__(self):
        self.neural_evaluator = NeuralEvaluator()
        self.material_evaluator = MaterialEvaluator()
        self.mcts_engine = MCTSEngine(self.material_evaluator)
        
        self.training_games: List[TrainingGame] = []
        self.training_data: List[TrainingData] = []
        
        # Training statistics
        self.training_stats = {
            "games_played": 0,
            "positions_trained": 0,
            "training_iterations": 0,
            "best_loss": float('inf'),
            "win_rate_history": []
        }
    
    def self_play_game(self, time_per_move: float = 1.0) -> TrainingGame:
        """Play a game against itself"""
        board = chess.Board()
        moves = []
        positions = []
        evaluations = []
        
        logger.info("Starting self-play game")
        
        while not board.is_game_over() and len(moves) < config.game.max_moves:
            # Store current position
            positions.append(board.fen())
            
            # Get move from MCTS
            try:
                move, confidence, pv = self.mcts_engine.search(
                    board, 
                    time_limit=time_per_move
                )
                
                # Evaluate position
                evaluation = self.neural_evaluator.evaluate(board)
                evaluations.append(evaluation)
                
                # Make move
                moves.append(move)
                board.push(move)
                
                logger.debug(f"Move {len(moves)}: {move}, eval: {evaluation:.2f}")
                
            except Exception as e:
                logger.error(f"Error in self-play: {e}")
                break
        
        # Create training game
        game = TrainingGame(
            moves=moves,
            positions=positions,
            evaluations=evaluations,
            result=board.result(),
            game_length=len(moves),
            timestamp=time.time()
        )
        
        logger.info(f"Self-play game completed: {game.result}, {game.game_length} moves")
        return game
    
    def generate_training_data(self, games: List[TrainingGame]) -> List[TrainingData]:
        """Convert games to training data"""
        training_data = []
        
        for game in games:
            # Convert result to numerical value
            if game.result == "1-0":
                white_result = 1.0
                black_result = -1.0
            elif game.result == "0-1":
                white_result = -1.0
                black_result = 1.0
            else:
                white_result = 0.0
                black_result = 0.0
            
            # Create training data for each position
            for i, (position, evaluation) in enumerate(zip(game.positions, game.evaluations)):
                board = chess.Board(position)
                
                # Result from current player's perspective
                result = white_result if board.turn else black_result
                
                training_data.append(TrainingData(
                    position=position,
                    evaluation=evaluation,
                    result=result,
                    move_number=i + 1
                ))
        
        return training_data
    
    def train_neural_network(self, training_data: List[TrainingData], epochs: int = None) -> Dict[str, float]:
        """Train the neural network on collected data"""
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available, skipping neural network training")
            return {
                "average_loss": 0.0,
                "epochs": 0,
                "training_samples": len(training_data),
                "note": "PyTorch not available"
            }

        if epochs is None:
            epochs = config.ai.nn_epochs

        logger.info(f"Training neural network on {len(training_data)} positions")

        try:
            # Create dataset and dataloader
            dataset = ChessDataset(training_data)
            dataloader = DataLoader(
                dataset,
                batch_size=config.ai.nn_batch_size,
                shuffle=True
            )

            # Setup training
            model = self.neural_evaluator.model
            optimizer = optim.Adam(model.parameters(), lr=config.ai.nn_learning_rate)
            criterion = nn.MSELoss()

            model.train()
            total_loss = 0.0

            for epoch in tqdm(range(epochs), desc="Training"):
                epoch_loss = 0.0

                for batch_features, batch_targets in dataloader:
                    batch_features = batch_features.to(self.neural_evaluator.device)
                    batch_targets = batch_targets.to(self.neural_evaluator.device)

                    # Forward pass
                    optimizer.zero_grad()
                    outputs = model(batch_features)
                    loss = criterion(outputs, batch_targets)

                    # Backward pass
                    loss.backward()
                    optimizer.step()

                    epoch_loss += loss.item()

                total_loss += epoch_loss

                if epoch % 10 == 0:
                    avg_loss = epoch_loss / len(dataloader)
                    logger.info(f"Epoch {epoch}, Loss: {avg_loss:.6f}")

            model.eval()
            avg_loss = total_loss / (epochs * len(dataloader))

            # Update best loss
            if avg_loss < self.training_stats["best_loss"]:
                self.training_stats["best_loss"] = avg_loss
                self.save_model("best_model.pth")

            logger.info(f"Training completed. Average loss: {avg_loss:.6f}")

            return {
                "average_loss": avg_loss,
                "epochs": epochs,
                "training_samples": len(training_data)
            }
        except Exception as e:
            logger.error(f"Training failed: {e}")
            return {
                "average_loss": float('inf'),
                "epochs": 0,
                "training_samples": len(training_data),
                "error": str(e)
            }
    
    def run_training_iteration(self, num_games: int = None) -> Dict[str, any]:
        """Run a complete training iteration"""
        if num_games is None:
            num_games = config.ai.self_play_games
        
        logger.info(f"Starting training iteration with {num_games} self-play games")
        
        # Generate self-play games
        new_games = []
        for i in tqdm(range(num_games), desc="Self-play"):
            game = self.self_play_game()
            new_games.append(game)
            self.training_games.append(game)
        
        # Generate training data
        new_training_data = self.generate_training_data(new_games)
        self.training_data.extend(new_training_data)
        
        # Train neural network
        training_results = self.train_neural_network(new_training_data)
        
        # Update statistics
        self.training_stats["games_played"] += num_games
        self.training_stats["positions_trained"] += len(new_training_data)
        self.training_stats["training_iterations"] += 1
        
        # Calculate win rate
        white_wins = sum(1 for game in new_games if game.result == "1-0")
        black_wins = sum(1 for game in new_games if game.result == "0-1")
        draws = sum(1 for game in new_games if game.result == "1/2-1/2")
        
        win_rate = (white_wins + black_wins) / num_games if num_games > 0 else 0
        self.training_stats["win_rate_history"].append(win_rate)
        
        iteration_results = {
            "iteration": self.training_stats["training_iterations"],
            "games_played": num_games,
            "new_positions": len(new_training_data),
            "training_loss": training_results["average_loss"],
            "win_rate": win_rate,
            "white_wins": white_wins,
            "black_wins": black_wins,
            "draws": draws,
            "average_game_length": np.mean([game.game_length for game in new_games])
        }
        
        logger.info(f"Training iteration completed: {iteration_results}")
        
        # Save progress
        if self.training_stats["training_iterations"] % config.ai.save_frequency == 0:
            self.save_progress()
        
        return iteration_results
    
    def save_model(self, filename: str):
        """Save the neural network model"""
        model_path = os.path.join(config.logging.models_dir, filename)
        torch.save(self.neural_evaluator.model.state_dict(), model_path)
        logger.info(f"Model saved to {model_path}")
    
    def load_model(self, filename: str):
        """Load a neural network model"""
        model_path = os.path.join(config.logging.models_dir, filename)
        if os.path.exists(model_path):
            self.neural_evaluator.model.load_state_dict(
                torch.load(model_path, map_location=self.neural_evaluator.device)
            )
            logger.info(f"Model loaded from {model_path}")
        else:
            logger.warning(f"Model file not found: {model_path}")
    
    def save_progress(self):
        """Save training progress and data"""
        # Save training statistics
        stats_path = os.path.join(config.logging.logs_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2)
        
        # Save training games
        games_path = os.path.join(config.logging.games_dir, "training_games.json")
        games_data = [asdict(game) for game in self.training_games]
        with open(games_path, 'w') as f:
            json.dump(games_data, f, indent=2)
        
        # Save current model
        self.save_model(f"model_iteration_{self.training_stats['training_iterations']}.pth")
        
        logger.info("Training progress saved")
    
    def load_progress(self):
        """Load previous training progress"""
        # Load training statistics
        stats_path = os.path.join(config.logging.logs_dir, "training_stats.json")
        if os.path.exists(stats_path):
            with open(stats_path, 'r') as f:
                self.training_stats = json.load(f)
        
        # Load training games
        games_path = os.path.join(config.logging.games_dir, "training_games.json")
        if os.path.exists(games_path):
            with open(games_path, 'r') as f:
                games_data = json.load(f)
                self.training_games = [TrainingGame(**game) for game in games_data]
        
        logger.info("Training progress loaded")
