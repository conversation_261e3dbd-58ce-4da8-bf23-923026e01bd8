"""
Advanced Chess Engine with Hybrid Architecture
Combines Minimax, Neural Networks, and Monte Carlo Tree Search
"""
import chess
import chess.engine
import chess.polyglot
import numpy as np
import time
import random
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging

from config import config

# Setup logging
logging.basicConfig(level=getattr(logging, config.logging.log_level))
logger = logging.getLogger(__name__)

@dataclass
class MoveEvaluation:
    """Container for move evaluation results"""
    move: chess.Move
    score: float
    depth: int
    time_taken: float
    principal_variation: List[chess.Move]
    evaluation_type: str  # "minimax", "neural", "mcts", "hybrid"

class ChessEvaluator(ABC):
    """Abstract base class for position evaluators"""
    
    @abstractmethod
    def evaluate(self, board: chess.Board) -> float:
        """Evaluate a chess position. Returns score from white's perspective."""
        pass

class MaterialEvaluator(ChessEvaluator):
    """Traditional material-based evaluator"""
    
    PIECE_VALUES = {
        chess.PAWN: 100,
        chess.KNIGHT: 320,
        chess.BISHOP: 330,
        chess.ROOK: 500,
        chess.QUEEN: 900,
        chess.KING: 20000
    }
    
    # Positional bonus tables
    PAWN_TABLE = [
        0,  0,  0,  0,  0,  0,  0,  0,
        50, 50, 50, 50, 50, 50, 50, 50,
        10, 10, 20, 30, 30, 20, 10, 10,
        5,  5, 10, 25, 25, 10,  5,  5,
        0,  0,  0, 20, 20,  0,  0,  0,
        5, -5,-10,  0,  0,-10, -5,  5,
        5, 10, 10,-20,-20, 10, 10,  5,
        0,  0,  0,  0,  0,  0,  0,  0
    ]
    
    KNIGHT_TABLE = [
        -50,-40,-30,-30,-30,-30,-40,-50,
        -40,-20,  0,  0,  0,  0,-20,-40,
        -30,  0, 10, 15, 15, 10,  0,-30,
        -30,  5, 15, 20, 20, 15,  5,-30,
        -30,  0, 15, 20, 20, 15,  0,-30,
        -30,  5, 10, 15, 15, 10,  5,-30,
        -40,-20,  0,  5,  5,  0,-20,-40,
        -50,-40,-30,-30,-30,-30,-40,-50
    ]
    
    def evaluate(self, board: chess.Board) -> float:
        """Evaluate position using material and positional factors"""
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.PIECE_VALUES[piece.piece_type]
                
                # Add positional bonus
                if piece.piece_type == chess.PAWN:
                    pos_bonus = self.PAWN_TABLE[square if piece.color else 63 - square]
                elif piece.piece_type == chess.KNIGHT:
                    pos_bonus = self.KNIGHT_TABLE[square if piece.color else 63 - square]
                else:
                    pos_bonus = 0
                
                total_value = value + pos_bonus
                score += total_value if piece.color else -total_value
        
        # Additional positional factors
        score += self._evaluate_king_safety(board)
        score += self._evaluate_pawn_structure(board)
        score += self._evaluate_piece_activity(board)
        
        return score / 100.0  # Normalize to centipawns
    
    def _evaluate_king_safety(self, board: chess.Board) -> int:
        """Evaluate king safety"""
        score = 0
        
        for color in [chess.WHITE, chess.BLACK]:
            king_square = board.king(color)
            if king_square:
                # Penalty for exposed king
                attackers = len(board.attackers(not color, king_square))
                score += (-50 * attackers) if color else (50 * attackers)
        
        return score
    
    def _evaluate_pawn_structure(self, board: chess.Board) -> int:
        """Evaluate pawn structure"""
        score = 0
        
        white_pawns = board.pieces(chess.PAWN, chess.WHITE)
        black_pawns = board.pieces(chess.PAWN, chess.BLACK)
        
        # Doubled pawns penalty
        for file in range(8):
            white_file_pawns = len([sq for sq in white_pawns if chess.square_file(sq) == file])
            black_file_pawns = len([sq for sq in black_pawns if chess.square_file(sq) == file])
            
            if white_file_pawns > 1:
                score -= 20 * (white_file_pawns - 1)
            if black_file_pawns > 1:
                score += 20 * (black_file_pawns - 1)
        
        return score
    
    def _evaluate_piece_activity(self, board: chess.Board) -> int:
        """Evaluate piece activity and mobility"""
        score = 0
        
        # Count legal moves (mobility)
        white_mobility = len(list(board.legal_moves)) if board.turn else 0
        board.push(chess.Move.null())
        black_mobility = len(list(board.legal_moves)) if not board.turn else 0
        board.pop()
        
        score += (white_mobility - black_mobility) * 2
        
        return score

class TranspositionTable:
    """Transposition table for caching position evaluations"""
    
    def __init__(self, size: int = 1000000):
        self.table: Dict[int, Tuple[float, int, str]] = {}
        self.size = size
    
    def get(self, board_hash: int, depth: int) -> Optional[float]:
        """Get cached evaluation if available and deep enough"""
        if board_hash in self.table:
            score, cached_depth, node_type = self.table[board_hash]
            if cached_depth >= depth:
                return score
        return None
    
    def store(self, board_hash: int, score: float, depth: int, node_type: str = "exact"):
        """Store evaluation in transposition table"""
        if len(self.table) >= self.size:
            # Simple replacement strategy: remove random entry
            random_key = random.choice(list(self.table.keys()))
            del self.table[random_key]
        
        self.table[board_hash] = (score, depth, node_type)

class MinimaxEngine:
    """Advanced Minimax engine with alpha-beta pruning and enhancements"""
    
    def __init__(self, evaluator: ChessEvaluator):
        self.evaluator = evaluator
        self.transposition_table = TranspositionTable() if config.ai.use_transposition_table else None
        self.nodes_searched = 0
        self.time_limit = None
        self.start_time = None
    
    def search(self, board: chess.Board, depth: int, time_limit: Optional[float] = None) -> MoveEvaluation:
        """Search for the best move using minimax with alpha-beta pruning"""
        self.nodes_searched = 0
        self.time_limit = time_limit
        self.start_time = time.time()
        
        best_move = None
        best_score = float('-inf')
        principal_variation = []
        
        legal_moves = list(board.legal_moves)
        if not legal_moves:
            raise ValueError("No legal moves available")
        
        # Move ordering: try captures and checks first
        legal_moves.sort(key=lambda move: self._move_priority(board, move), reverse=True)
        
        for move in legal_moves:
            if self._time_exceeded():
                break
                
            board.push(move)
            score = -self._minimax(board, depth - 1, float('-inf'), float('inf'), False)
            board.pop()
            
            if score > best_score:
                best_score = score
                best_move = move
                principal_variation = [move]
        
        time_taken = time.time() - self.start_time
        
        return MoveEvaluation(
            move=best_move,
            score=best_score,
            depth=depth,
            time_taken=time_taken,
            principal_variation=principal_variation,
            evaluation_type="minimax"
        )
    
    def _minimax(self, board: chess.Board, depth: int, alpha: float, beta: float, maximizing: bool) -> float:
        """Minimax algorithm with alpha-beta pruning"""
        if self._time_exceeded():
            return 0
        
        self.nodes_searched += 1
        
        # Check transposition table
        board_hash = chess.polyglot.zobrist_hash(board)
        if self.transposition_table:
            cached_score = self.transposition_table.get(board_hash, depth)
            if cached_score is not None:
                return cached_score
        
        # Terminal conditions
        if depth == 0 or board.is_game_over():
            score = self.evaluator.evaluate(board)
            if self.transposition_table:
                self.transposition_table.store(board_hash, score, depth)
            return score
        
        legal_moves = list(board.legal_moves)
        legal_moves.sort(key=lambda move: self._move_priority(board, move), reverse=True)
        
        if maximizing:
            max_eval = float('-inf')
            for move in legal_moves:
                board.push(move)
                eval_score = self._minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                max_eval = max(max_eval, eval_score)
                alpha = max(alpha, eval_score)
                
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            if self.transposition_table:
                self.transposition_table.store(board_hash, max_eval, depth)
            return max_eval
        else:
            min_eval = float('inf')
            for move in legal_moves:
                board.push(move)
                eval_score = self._minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                min_eval = min(min_eval, eval_score)
                beta = min(beta, eval_score)
                
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            if self.transposition_table:
                self.transposition_table.store(board_hash, min_eval, depth)
            return min_eval
    
    def _move_priority(self, board: chess.Board, move: chess.Move) -> int:
        """Calculate move priority for ordering"""
        priority = 0
        
        # Captures
        if board.is_capture(move):
            captured_piece = board.piece_at(move.to_square)
            if captured_piece:
                priority += MaterialEvaluator.PIECE_VALUES[captured_piece.piece_type]
        
        # Checks
        board.push(move)
        if board.is_check():
            priority += 100
        board.pop()
        
        # Promotions
        if move.promotion:
            priority += MaterialEvaluator.PIECE_VALUES[move.promotion]
        
        return priority
    
    def _time_exceeded(self) -> bool:
        """Check if time limit has been exceeded"""
        if self.time_limit and self.start_time:
            return time.time() - self.start_time > self.time_limit
        return False


class NeuralEvaluator(ChessEvaluator):
    """Neural network-based position evaluator"""

    def __init__(self, model_path: Optional[str] = None):
        try:
            import torch
            import torch.nn as nn

            self.device = torch.device("cuda" if torch.cuda.is_available() and config.use_gpu else "cpu")
            self.model = self._create_model()

            if model_path and os.path.exists(model_path):
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))

            self.model.to(self.device)
            self.model.eval()
            self.torch_available = True
        except ImportError:
            logger.warning("PyTorch not available, using fallback evaluation")
            self.torch_available = False
            self.fallback_evaluator = MaterialEvaluator()

    def _create_model(self):
        """Create neural network model"""
        import torch.nn as nn

        class ChessNet(nn.Module):
            def __init__(self):
                super().__init__()

                # Input: 8x8x12 (board representation) + additional features
                input_size = 8 * 8 * 12 + 64  # board + additional features

                layers = []
                prev_size = input_size

                for hidden_size in config.ai.nn_hidden_layers:
                    layers.extend([
                        nn.Linear(prev_size, hidden_size),
                        nn.ReLU(),
                        nn.Dropout(0.2)
                    ])
                    prev_size = hidden_size

                # Output layer
                layers.append(nn.Linear(prev_size, 1))
                layers.append(nn.Tanh())  # Output between -1 and 1

                self.network = nn.Sequential(*layers)

            def forward(self, x):
                return self.network(x)

        return ChessNet()

    def evaluate(self, board: chess.Board) -> float:
        """Evaluate position using neural network"""
        if not self.torch_available:
            return self.fallback_evaluator.evaluate(board)

        try:
            import torch

            features = self._board_to_features(board)
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

            with torch.no_grad():
                output = self.model(features_tensor)
                score = output.item() * 1000  # Scale to centipawns

            return score
        except Exception as e:
            logger.warning(f"Neural evaluation failed: {e}")
            return self.fallback_evaluator.evaluate(board)

    def _board_to_features(self, board: chess.Board) -> np.ndarray:
        """Convert board position to feature vector"""
        # 8x8x12 board representation (6 piece types x 2 colors)
        board_features = np.zeros((8, 8, 12))

        piece_map = {
            chess.PAWN: 0, chess.KNIGHT: 1, chess.BISHOP: 2,
            chess.ROOK: 3, chess.QUEEN: 4, chess.KING: 5
        }

        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row, col = divmod(square, 8)
                piece_idx = piece_map[piece.piece_type]
                color_offset = 0 if piece.color else 6
                board_features[row, col, piece_idx + color_offset] = 1

        # Flatten board features
        board_flat = board_features.flatten()

        # Additional features
        additional_features = np.zeros(64)

        # Castling rights
        additional_features[0] = int(board.has_kingside_castling_rights(chess.WHITE))
        additional_features[1] = int(board.has_queenside_castling_rights(chess.WHITE))
        additional_features[2] = int(board.has_kingside_castling_rights(chess.BLACK))
        additional_features[3] = int(board.has_queenside_castling_rights(chess.BLACK))

        # En passant
        additional_features[4] = int(board.ep_square is not None)

        # Turn
        additional_features[5] = int(board.turn)

        # Game phase (rough estimate based on material)
        material_count = len(board.piece_map())
        additional_features[6] = material_count / 32.0  # Normalize

        # Combine all features
        return np.concatenate([board_flat, additional_features])


class HybridEvaluator(ChessEvaluator):
    """Hybrid evaluator combining traditional and neural approaches"""

    def __init__(self, neural_weight: float = 0.7):
        self.material_evaluator = MaterialEvaluator()
        self.neural_evaluator = NeuralEvaluator()
        self.neural_weight = neural_weight
        self.material_weight = 1.0 - neural_weight

    def evaluate(self, board: chess.Board) -> float:
        """Combine evaluations from multiple sources"""
        try:
            neural_score = self.neural_evaluator.evaluate(board)
            material_score = self.material_evaluator.evaluate(board)

            # Weighted combination
            combined_score = (
                self.neural_weight * neural_score +
                self.material_weight * material_score
            )

            return combined_score
        except Exception as e:
            logger.warning(f"Neural evaluation failed, falling back to material: {e}")
            return self.material_evaluator.evaluate(board)
