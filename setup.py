#!/usr/bin/env python3
"""
Setup script for Advanced Chess AI
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def create_virtual_environment():
    """Create virtual environment"""
    venv_path = "venv"
    if os.path.exists(venv_path):
        print("✓ Virtual environment already exists")
        return venv_path

    print("Creating virtual environment...")
    try:
        subprocess.check_call([sys.executable, "-m", "venv", venv_path])
        print("✓ Virtual environment created")
        return venv_path
    except subprocess.CalledProcessError:
        print("Error: Failed to create virtual environment")
        sys.exit(1)

def install_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")

    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        pip_cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    else:
        # Try to use virtual environment
        venv_path = create_virtual_environment()
        if platform.system() == "Windows":
            pip_cmd = [os.path.join(venv_path, "Scripts", "python.exe"), "-m", "pip", "install", "-r", "requirements.txt"]
        else:
            pip_cmd = [os.path.join(venv_path, "bin", "python"), "-m", "pip", "install", "-r", "requirements.txt"]

    try:
        subprocess.check_call(pip_cmd)
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("Error: Failed to install dependencies")
        print("Please create a virtual environment manually:")
        print("  python3 -m venv venv")
        print("  source venv/bin/activate  # On Windows: venv\\Scripts\\activate")
        print("  pip install -r requirements.txt")
        sys.exit(1)

def setup_directories():
    """Create necessary directories"""
    directories = [
        "games",
        "models", 
        "logs",
        "analysis",
        "tablebases"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")

def download_stockfish():
    """Download and setup Stockfish engine"""
    system = platform.system().lower()
    
    if system == "linux":
        print("Installing Stockfish on Linux...")
        try:
            # Try to install via package manager
            subprocess.run(["sudo", "apt-get", "update"], check=False)
            result = subprocess.run(["sudo", "apt-get", "install", "-y", "stockfish"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Stockfish installed via apt")
                return
        except:
            pass
    
    elif system == "darwin":  # macOS
        print("Installing Stockfish on macOS...")
        try:
            result = subprocess.run(["brew", "install", "stockfish"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Stockfish installed via Homebrew")
                return
        except:
            pass
    
    elif system == "windows":
        print("Please download Stockfish manually from https://stockfishchess.org/download/")
        print("Extract it and update the engine path in config.py")
        return
    
    print("⚠ Could not automatically install Stockfish")
    print("Please install it manually and update the path in config.py")

def create_example_config():
    """Create example configuration file"""
    example_config = """
# Example configuration for Advanced Chess AI
# Copy this to config_local.py and modify as needed

from config import config

# Update Stockfish path if needed
config.engine.stockfish_path = "/usr/games/stockfish"  # Linux
# config.engine.stockfish_path = "/usr/local/bin/stockfish"  # macOS
# config.engine.stockfish_path = "C:\\stockfish\\stockfish.exe"  # Windows

# Adjust AI settings
config.ai.minimax_depth = 4
config.ai.mcts_simulations = 1000
config.ai.nn_learning_rate = 0.001

# Training settings
config.ai.self_play_games = 50
config.ai.training_iterations = 10

print("Configuration loaded successfully!")
"""
    
    with open("config_example.py", "w") as f:
        f.write(example_config)
    
    print("✓ Created config_example.py")

def run_tests():
    """Run basic tests to verify installation"""
    print("Running basic tests...")
    
    try:
        # Test imports
        import chess
        import numpy as np
        import torch
        print("✓ Core dependencies imported successfully")
        
        # Test chess engine creation
        from chess_engine import MaterialEvaluator, MinimaxEngine
        evaluator = MaterialEvaluator()
        engine = MinimaxEngine(evaluator)
        print("✓ Chess engine components working")
        
        # Test basic position evaluation
        board = chess.Board()
        score = evaluator.evaluate(board)
        print(f"✓ Position evaluation working (score: {score})")
        
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False
    
    return True

def main():
    """Main setup function"""
    print("=== Advanced Chess AI Setup ===")
    print()
    
    # Check Python version
    check_python_version()
    
    # Setup directories
    setup_directories()
    
    # Install dependencies
    install_dependencies()
    
    # Download Stockfish
    download_stockfish()
    
    # Create example config
    create_example_config()
    
    # Run tests
    if run_tests():
        print()
        print("=== Setup Complete! ===")
        print()
        print("Quick start:")
        print("1. Interactive mode: python main.py interactive")
        print("2. Self-training: python main.py train --iterations 3 --games 5")
        print("3. Benchmark: python main.py benchmark --games 5")
        print("4. Analysis: python main.py analysis")
        print("5. UCI mode: python main.py uci")
        print()
        print("For more options: python main.py --help")
    else:
        print()
        print("Setup completed with some issues. Check the error messages above.")

if __name__ == "__main__":
    main()
