"""
Advanced analytics and logging system for chess AI
Provides comprehensive analysis and visualization capabilities
"""
import json
import os
import time
import chess
import chess.pgn
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

from config import config
from uci_interface import GameResult
from self_training import TrainingGame

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    win_rate: float
    average_game_length: float
    blunder_rate: float
    tactical_accuracy: float
    positional_score: float
    time_usage: float
    move_quality: float
    elo_estimate: Optional[float] = None

@dataclass
class EngineComparison:
    """Comparison results against different engines"""
    engine_name: str
    engine_level: int
    games_played: int
    wins: int
    losses: int
    draws: int
    win_rate: float
    average_game_length: float
    performance_rating: float

class ChessAnalyzer:
    """Advanced chess game analyzer"""
    
    def __init__(self):
        self.game_database: List[GameResult] = []
        self.training_database: List[TrainingGame] = []
        self.performance_history: List[PerformanceMetrics] = []
        
    def load_games(self, filepath: str):
        """Load games from JSON file"""
        try:
            with open(filepath, 'r') as f:
                games_data = json.load(f)
                self.game_database = [GameResult(**game) for game in games_data]
            logger.info(f"Loaded {len(self.game_database)} games from {filepath}")
        except Exception as e:
            logger.error(f"Error loading games: {e}")
    
    def analyze_move_quality(self, moves: List[str], time_controls: List[float] = None) -> Dict[str, float]:
        """Analyze the quality of moves in a game"""
        if not moves:
            return {}
        
        board = chess.Board()
        move_scores = []
        blunders = 0
        
        for i, move_str in enumerate(moves):
            try:
                move = chess.Move.from_uci(move_str)
                if move not in board.legal_moves:
                    continue
                
                # Simple move quality assessment
                # In a real implementation, this would use a strong engine for analysis
                move_score = self._evaluate_move_quality(board, move)
                move_scores.append(move_score)
                
                # Count blunders (moves with score < -200 centipawns)
                if move_score < -200:
                    blunders += 1
                
                board.push(move)
                
            except ValueError:
                continue
        
        if not move_scores:
            return {}
        
        return {
            "average_move_quality": np.mean(move_scores),
            "move_consistency": np.std(move_scores),
            "blunder_count": blunders,
            "blunder_rate": blunders / len(move_scores),
            "best_move_score": max(move_scores),
            "worst_move_score": min(move_scores)
        }
    
    def _evaluate_move_quality(self, board: chess.Board, move: chess.Move) -> float:
        """Evaluate the quality of a single move"""
        # Simplified move evaluation
        # In practice, this would use a strong engine like Stockfish
        
        score = 0
        
        # Capture bonus
        if board.is_capture(move):
            captured_piece = board.piece_at(move.to_square)
            if captured_piece:
                piece_values = {chess.PAWN: 100, chess.KNIGHT: 300, chess.BISHOP: 300,
                              chess.ROOK: 500, chess.QUEEN: 900}
                score += piece_values.get(captured_piece.piece_type, 0)
        
        # Check bonus
        board.push(move)
        if board.is_check():
            score += 50
        
        # Checkmate bonus
        if board.is_checkmate():
            score += 10000
        
        board.pop()
        
        # Center control
        center_squares = [chess.E4, chess.E5, chess.D4, chess.D5]
        if move.to_square in center_squares:
            score += 20
        
        return score
    
    def calculate_performance_metrics(self, games: List[GameResult]) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        if not games:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0)
        
        # Win rate calculation
        our_wins = 0
        total_games = len(games)
        
        for game in games:
            if ((game.white_player == "Our AI" and game.result == "1-0") or
                (game.black_player == "Our AI" and game.result == "0-1")):
                our_wins += 1
        
        win_rate = our_wins / total_games
        
        # Average game length
        avg_game_length = np.mean([game.game_length for game in games])
        
        # Move quality analysis
        all_move_qualities = []
        total_blunders = 0
        total_moves = 0
        
        for game in games:
            move_analysis = self.analyze_move_quality(game.moves)
            if move_analysis:
                all_move_qualities.append(move_analysis["average_move_quality"])
                total_blunders += move_analysis["blunder_count"]
                total_moves += len(game.moves)
        
        blunder_rate = total_blunders / total_moves if total_moves > 0 else 0
        tactical_accuracy = np.mean(all_move_qualities) if all_move_qualities else 0
        
        # Positional score (simplified)
        positional_score = self._calculate_positional_score(games)
        
        # Time usage (placeholder)
        time_usage = 0.8  # Assume 80% of allocated time used
        
        # Overall move quality
        move_quality = tactical_accuracy / 100.0 if tactical_accuracy > 0 else 0
        
        return PerformanceMetrics(
            win_rate=win_rate,
            average_game_length=avg_game_length,
            blunder_rate=blunder_rate,
            tactical_accuracy=tactical_accuracy,
            positional_score=positional_score,
            time_usage=time_usage,
            move_quality=move_quality
        )
    
    def _calculate_positional_score(self, games: List[GameResult]) -> float:
        """Calculate positional understanding score"""
        # Simplified positional evaluation
        # In practice, this would analyze pawn structure, piece activity, etc.
        
        scores = []
        for game in games:
            if "material_balance" in game.analysis:
                # Convert material balance to positional score
                balance = game.analysis["material_balance"]
                score = 50 + balance * 5  # Normalize around 50
                scores.append(max(0, min(100, score)))
        
        return np.mean(scores) if scores else 50.0
    
    def compare_against_engines(self, engine_results: Dict[str, List[GameResult]]) -> List[EngineComparison]:
        """Compare performance against different engines"""
        comparisons = []
        
        for engine_name, games in engine_results.items():
            if not games:
                continue
            
            wins = sum(1 for game in games if 
                      (game.white_player == "Our AI" and game.result == "1-0") or
                      (game.black_player == "Our AI" and game.result == "0-1"))
            
            losses = sum(1 for game in games if 
                        (game.white_player == "Our AI" and game.result == "0-1") or
                        (game.black_player == "Our AI" and game.result == "1-0"))
            
            draws = sum(1 for game in games if game.result == "1/2-1/2")
            
            total_games = len(games)
            win_rate = wins / total_games if total_games > 0 else 0
            avg_game_length = np.mean([game.game_length for game in games])
            
            # Calculate performance rating (simplified Elo estimation)
            performance_rating = self._estimate_performance_rating(wins, losses, draws)
            
            comparison = EngineComparison(
                engine_name=engine_name,
                engine_level=1,  # Would need to be specified
                games_played=total_games,
                wins=wins,
                losses=losses,
                draws=draws,
                win_rate=win_rate,
                average_game_length=avg_game_length,
                performance_rating=performance_rating
            )
            
            comparisons.append(comparison)
        
        return comparisons
    
    def _estimate_performance_rating(self, wins: int, losses: int, draws: int) -> float:
        """Estimate Elo performance rating"""
        total_games = wins + losses + draws
        if total_games == 0:
            return 1200  # Default rating
        
        score = (wins + 0.5 * draws) / total_games
        
        # Simple Elo estimation
        if score >= 1.0:
            return 2000  # Very strong
        elif score <= 0.0:
            return 800   # Very weak
        else:
            # Convert score to Elo difference
            elo_diff = -400 * np.log10(1/score - 1)
            return 1500 + elo_diff  # Assume opponent is 1500
    
    def generate_training_report(self, training_data: List[TrainingGame]) -> Dict[str, Any]:
        """Generate comprehensive training report"""
        if not training_data:
            return {}
        
        # Basic statistics
        total_games = len(training_data)
        total_positions = sum(len(game.positions) for game in training_data)
        avg_game_length = np.mean([game.game_length for game in training_data])
        
        # Result distribution
        white_wins = sum(1 for game in training_data if game.result == "1-0")
        black_wins = sum(1 for game in training_data if game.result == "0-1")
        draws = sum(1 for game in training_data if game.result == "1/2-1/2")
        
        # Game length distribution
        game_lengths = [game.game_length for game in training_data]
        
        # Evaluation trends
        all_evaluations = []
        for game in training_data:
            all_evaluations.extend(game.evaluations)
        
        report = {
            "training_summary": {
                "total_games": total_games,
                "total_positions": total_positions,
                "average_game_length": avg_game_length,
                "white_wins": white_wins,
                "black_wins": black_wins,
                "draws": draws,
                "draw_rate": draws / total_games if total_games > 0 else 0
            },
            "game_length_stats": {
                "min": min(game_lengths) if game_lengths else 0,
                "max": max(game_lengths) if game_lengths else 0,
                "mean": np.mean(game_lengths) if game_lengths else 0,
                "std": np.std(game_lengths) if game_lengths else 0
            },
            "evaluation_stats": {
                "min": min(all_evaluations) if all_evaluations else 0,
                "max": max(all_evaluations) if all_evaluations else 0,
                "mean": np.mean(all_evaluations) if all_evaluations else 0,
                "std": np.std(all_evaluations) if all_evaluations else 0
            }
        }
        
        return report
    
    def create_visualizations(self, output_dir: str = None):
        """Create visualization plots"""
        if output_dir is None:
            output_dir = config.logging.analysis_dir
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Performance over time
        if self.performance_history:
            self._plot_performance_trends(output_dir)
        
        # Game results distribution
        if self.game_database:
            self._plot_game_results(output_dir)
            self._plot_game_length_distribution(output_dir)
        
        logger.info(f"Visualizations saved to {output_dir}")
    
    def _plot_performance_trends(self, output_dir: str):
        """Plot performance trends over time"""
        metrics = ['win_rate', 'tactical_accuracy', 'move_quality']
        
        fig, axes = plt.subplots(len(metrics), 1, figsize=(12, 8))
        
        for i, metric in enumerate(metrics):
            values = [getattr(perf, metric) for perf in self.performance_history]
            axes[i].plot(values, marker='o')
            axes[i].set_title(f'{metric.replace("_", " ").title()} Over Time')
            axes[i].set_ylabel(metric.replace("_", " ").title())
            axes[i].grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'performance_trends.png'))
        plt.close()
    
    def _plot_game_results(self, output_dir: str):
        """Plot game results distribution"""
        results = [game.result for game in self.game_database]
        result_counts = pd.Series(results).value_counts()
        
        plt.figure(figsize=(8, 6))
        result_counts.plot(kind='bar')
        plt.title('Game Results Distribution')
        plt.xlabel('Result')
        plt.ylabel('Count')
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'game_results.png'))
        plt.close()
    
    def _plot_game_length_distribution(self, output_dir: str):
        """Plot game length distribution"""
        game_lengths = [game.game_length for game in self.game_database]
        
        plt.figure(figsize=(10, 6))
        plt.hist(game_lengths, bins=20, alpha=0.7, edgecolor='black')
        plt.title('Game Length Distribution')
        plt.xlabel('Number of Moves')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'game_length_distribution.png'))
        plt.close()
    
    def export_analysis(self, filename: str = None) -> str:
        """Export comprehensive analysis to JSON"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chess_ai_analysis_{timestamp}.json"
        
        filepath = os.path.join(config.logging.analysis_dir, filename)
        
        # Calculate current performance metrics
        current_metrics = self.calculate_performance_metrics(self.game_database)
        
        # Generate training report
        training_report = self.generate_training_report(self.training_database)
        
        # Engine comparisons
        engine_results = {}  # Would be populated with actual engine comparison data
        engine_comparisons = self.compare_against_engines(engine_results)
        
        analysis_data = {
            "timestamp": datetime.now().isoformat(),
            "current_performance": asdict(current_metrics),
            "training_report": training_report,
            "engine_comparisons": [asdict(comp) for comp in engine_comparisons],
            "total_games_analyzed": len(self.game_database),
            "performance_history": [asdict(perf) for perf in self.performance_history]
        }
        
        with open(filepath, 'w') as f:
            json.dump(analysis_data, f, indent=2)
        
        logger.info(f"Analysis exported to {filepath}")
        return filepath
