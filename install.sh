#!/bin/bash

# Advanced Chess AI Installation Script

echo "=== Advanced Chess AI Installation ==="
echo

# Check Python version
python3 --version
if [ $? -ne 0 ]; then
    echo "Error: Python 3 is required"
    exit 1
fi

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv venv
if [ $? -ne 0 ]; then
    echo "Error: Failed to create virtual environment"
    exit 1
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

# Create directories
echo "Creating directories..."
mkdir -p games models logs analysis tablebases

# Try to install Stockfish
echo "Installing Stockfish..."
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y stockfish
elif command -v brew &> /dev/null; then
    brew install stockfish
else
    echo "Please install Stockfish manually"
fi

# Test basic functionality
echo "Testing basic functionality..."
python3 -c "
import chess
import numpy as np
print('✓ Basic imports working')

from chess_engine import MaterialEvaluator
evaluator = MaterialEvaluator()
board = chess.Board()
score = evaluator.evaluate(board)
print(f'✓ Chess engine working (score: {score})')
"

if [ $? -eq 0 ]; then
    echo
    echo "=== Installation Complete! ==="
    echo
    echo "To use the Chess AI:"
    echo "1. Activate virtual environment: source venv/bin/activate"
    echo "2. Run interactive mode: python3 main.py interactive"
    echo "3. Run training: python3 main.py train --iterations 3 --games 5"
    echo "4. Run benchmark: python3 main.py benchmark --games 5"
    echo
    echo "For help: python3 main.py --help"
else
    echo "Installation completed with errors. Please check the output above."
fi
