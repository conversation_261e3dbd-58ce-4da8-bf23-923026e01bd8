#!/usr/bin/env python3
"""
Advanced Chess AI - Main Application
Modern chess AI with hybrid architecture, self-training, and comprehensive analytics
"""
import argparse
import sys
import os
import json
import time
import logging
from typing import Dict, List, Optional
import chess

# Import our modules
from config import config
from chess_engine import MaterialEvaluator, HybridEvaluator, MinimaxEngine
from mcts import MCTSEngine, MCTSPredictor
from self_training import SelfTrainer
from uci_interface import UCIEngine, EngineManager
from analytics import ChessAnalyzer

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.logging.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(config.logging.logs_dir, 'chess_ai.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ChessAI:
    """Main Chess AI application"""
    
    def __init__(self):
        self.evaluator = HybridEvaluator()
        self.minimax_engine = MinimaxEngine(self.evaluator)
        self.mcts_engine = MCTSEngine(self.evaluator)
        self.predictor = MCTSPredictor(self.evaluator)
        self.trainer = SelfTrainer()
        self.analyzer = ChessAnalyzer()
        self.engine_manager = EngineManager()
        
        logger.info("Chess AI initialized with hybrid architecture")
    
    def interactive_mode(self):
        """Interactive mode for playing against the AI"""
        print("=== Advanced Chess AI - Interactive Mode ===")
        print("Commands: move <move>, analyze, predict, quit")
        print("Example: move e2e4")
        
        board = chess.Board()
        
        while True:
            print(f"\nCurrent position:\n{board}")
            print(f"Turn: {'White' if board.turn else 'Black'}")
            
            if board.is_game_over():
                print(f"Game over! Result: {board.result()}")
                break
            
            command = input("\nEnter command: ").strip().lower()
            
            if command == "quit":
                break
            elif command == "analyze":
                self._analyze_position(board)
            elif command == "predict":
                self._predict_moves(board)
            elif command.startswith("move "):
                move_str = command[5:]
                if self._make_move(board, move_str):
                    # AI's turn
                    ai_move = self._get_ai_move(board)
                    if ai_move:
                        board.push(ai_move)
                        print(f"AI plays: {ai_move}")
            else:
                print("Unknown command. Use: move <move>, analyze, predict, quit")
    
    def _make_move(self, board: chess.Board, move_str: str) -> bool:
        """Make a move on the board"""
        try:
            move = chess.Move.from_uci(move_str)
            if move in board.legal_moves:
                board.push(move)
                return True
            else:
                print("Illegal move!")
                return False
        except ValueError:
            print("Invalid move format! Use format like 'e2e4'")
            return False
    
    def _get_ai_move(self, board: chess.Board) -> Optional[chess.Move]:
        """Get AI's move"""
        try:
            move, confidence, pv = self.mcts_engine.search(board, time_limit=3.0)
            print(f"AI confidence: {confidence:.3f}")
            if pv:
                pv_str = " ".join(str(m) for m in pv[:3])
                print(f"Principal variation: {pv_str}")
            return move
        except Exception as e:
            logger.error(f"Error getting AI move: {e}")
            return None
    
    def _analyze_position(self, board: chess.Board):
        """Analyze current position"""
        print("\n=== Position Analysis ===")
        
        # Basic evaluation
        evaluation = self.evaluator.evaluate(board)
        print(f"Position evaluation: {evaluation:.2f}")
        
        # MCTS analysis
        analysis = self.predictor.analyze_position(board)
        print(f"Best move: {analysis['best_move']}")
        print(f"Confidence: {analysis['confidence']:.3f}")
        
        if analysis['top_moves']:
            print("Top moves:")
            for move, prob in analysis['top_moves'][:3]:
                print(f"  {move}: {prob:.3f}")
    
    def _predict_moves(self, board: chess.Board):
        """Predict future moves"""
        print("\n=== Move Prediction ===")
        
        sequence = self.predictor.predict_sequence(board, steps=5)
        
        print("Predicted sequence:")
        for i, (move, confidence) in enumerate(sequence):
            print(f"  {i+1}. {move} (confidence: {confidence:.3f})")
    
    def train_mode(self, iterations: int = 5, games_per_iteration: int = 10):
        """Self-training mode"""
        print(f"=== Self-Training Mode ===")
        print(f"Running {iterations} iterations with {games_per_iteration} games each")
        
        for iteration in range(iterations):
            print(f"\n--- Training Iteration {iteration + 1}/{iterations} ---")
            
            results = self.trainer.run_training_iteration(games_per_iteration)
            
            print(f"Games played: {results['games_played']}")
            print(f"Training loss: {results['training_loss']:.6f}")
            print(f"Win rate: {results['win_rate']:.3f}")
            print(f"Average game length: {results['average_game_length']:.1f}")
            
            # Save progress periodically
            if (iteration + 1) % 2 == 0:
                self.trainer.save_progress()
                print("Progress saved")
    
    def benchmark_mode(self, engine_path: str = "/usr/games/stockfish", 
                      num_games: int = 10, time_control: float = 5.0):
        """Benchmark against other engines"""
        print(f"=== Benchmark Mode ===")
        print(f"Playing {num_games} games against {engine_path}")
        
        results = []
        
        for game_num in range(num_games):
            print(f"\nGame {game_num + 1}/{num_games}")
            
            # Alternate colors
            our_color = chess.WHITE if game_num % 2 == 0 else chess.BLACK
            color_str = "White" if our_color == chess.WHITE else "Black"
            print(f"Playing as {color_str}")
            
            try:
                result = self.engine_manager.play_against_engine(
                    engine_path, time_control, our_color
                )
                results.append(result)
                
                print(f"Result: {result.result}")
                print(f"Game length: {result.game_length} moves")
                
            except Exception as e:
                logger.error(f"Error in game {game_num + 1}: {e}")
                continue
        
        # Save results
        self.engine_manager.save_results(f"benchmark_{int(time.time())}.json")
        
        # Print statistics
        stats = self.engine_manager.get_statistics()
        print(f"\n=== Benchmark Results ===")
        print(f"Total games: {stats['total_games']}")
        print(f"Wins: {stats['wins']}")
        print(f"Losses: {stats['losses']}")
        print(f"Draws: {stats['draws']}")
        print(f"Win rate: {stats['win_rate']:.3f}")
        print(f"Average game length: {stats['average_game_length']:.1f}")
    
    def analysis_mode(self, games_file: str = None):
        """Analysis mode for examining games and performance"""
        print("=== Analysis Mode ===")
        
        if games_file and os.path.exists(games_file):
            self.analyzer.load_games(games_file)
            print(f"Loaded games from {games_file}")
        
        # Generate comprehensive analysis
        analysis_file = self.analyzer.export_analysis()
        print(f"Analysis exported to {analysis_file}")
        
        # Create visualizations
        self.analyzer.create_visualizations()
        print("Visualizations created")
    
    def uci_mode(self):
        """UCI mode for chess GUIs"""
        print("Starting UCI mode...")
        uci_engine = UCIEngine()
        uci_engine.uci_loop()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Advanced Chess AI")
    parser.add_argument("mode", choices=["interactive", "train", "benchmark", "analysis", "uci"],
                       help="Operating mode")
    parser.add_argument("--iterations", type=int, default=5,
                       help="Number of training iterations")
    parser.add_argument("--games", type=int, default=10,
                       help="Number of games per iteration or benchmark")
    parser.add_argument("--engine", type=str, default="/usr/games/stockfish",
                       help="Path to opponent engine")
    parser.add_argument("--time", type=float, default=5.0,
                       help="Time control in seconds")
    parser.add_argument("--file", type=str,
                       help="File path for analysis mode")
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize AI
    ai = ChessAI()
    
    try:
        if args.mode == "interactive":
            ai.interactive_mode()
        elif args.mode == "train":
            ai.train_mode(args.iterations, args.games)
        elif args.mode == "benchmark":
            ai.benchmark_mode(args.engine, args.games, args.time)
        elif args.mode == "analysis":
            ai.analysis_mode(args.file)
        elif args.mode == "uci":
            ai.uci_mode()
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
