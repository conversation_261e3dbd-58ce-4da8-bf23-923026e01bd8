#!/usr/bin/env python3
"""
Advanced Chess Evaluation with Complex Mathematics
Menggunakan tensor operations, signal processing, dan advanced algorithms
"""

import numpy as np
import math
import logging
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass
import chess
from config import config

logger = logging.getLogger(__name__)

@dataclass
class AdvancedEvaluationConfig:
    """Configuration for advanced evaluation"""
    use_tensor_operations: bool = True
    use_signal_processing: bool = True
    use_game_theory: bool = True
    use_information_theory: bool = True
    fourier_depth: int = 32
    wavelet_levels: int = 4
    entropy_window: int = 8
    minimax_depth_multiplier: float = 1.5

class TensorOperations:
    """Advanced tensor operations for chess evaluation"""
    
    @staticmethod
    def board_to_tensor(board: chess.Board) -> np.ndarray:
        """Convert board to multi-dimensional tensor"""
        # Create 8x8x12 tensor (8x8 board, 12 piece types)
        tensor = np.zeros((8, 8, 12), dtype=np.float32)
        
        piece_map = {
            chess.PAWN: 0, chess.ROOK: 1, chess.KNIGHT: 2,
            chess.BISHOP: 3, chess.QUEEN: 4, chess.KING: 5
        }
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row, col = divmod(square, 8)
                piece_idx = piece_map[piece.piece_type]
                color_offset = 0 if piece.color == chess.WHITE else 6
                tensor[row, col, piece_idx + color_offset] = 1.0
        
        return tensor
    
    @staticmethod
    def tensor_convolution(tensor: np.ndarray, kernel: np.ndarray) -> np.ndarray:
        """Apply convolution operation to detect patterns"""
        result = np.zeros_like(tensor[:, :, 0])
        
        for i in range(tensor.shape[0] - kernel.shape[0] + 1):
            for j in range(tensor.shape[1] - kernel.shape[1] + 1):
                for k in range(tensor.shape[2]):
                    patch = tensor[i:i+kernel.shape[0], j:j+kernel.shape[1], k]
                    result[i, j] += np.sum(patch * kernel)
        
        return result
    
    @staticmethod
    def create_pattern_kernels() -> Dict[str, np.ndarray]:
        """Create convolution kernels for chess pattern detection"""
        kernels = {}
        
        # Pawn chain detection
        kernels['pawn_chain'] = np.array([
            [0, 1, 0],
            [1, 0, 1],
            [0, 1, 0]
        ], dtype=np.float32)
        
        # Piece coordination
        kernels['coordination'] = np.array([
            [1, 1, 1],
            [1, 0, 1],
            [1, 1, 1]
        ], dtype=np.float32) / 8.0
        
        # Central control
        kernels['center_control'] = np.array([
            [0, 0, 0],
            [0, 1, 0],
            [0, 0, 0]
        ], dtype=np.float32)
        
        # Edge detection (for king safety)
        kernels['edge_detection'] = np.array([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ], dtype=np.float32)
        
        return kernels

class SignalProcessing:
    """Signal processing techniques for chess analysis"""
    
    @staticmethod
    def fourier_analysis(board_vector: np.ndarray, depth: int = 32) -> Dict[str, float]:
        """Analyze board using Fourier transform"""
        # Apply FFT to board representation
        fft_result = np.fft.fft(board_vector, n=depth)
        
        # Extract frequency domain features
        magnitude = np.abs(fft_result)
        phase = np.angle(fft_result)
        power_spectrum = magnitude ** 2
        
        # Calculate spectral features
        spectral_centroid = np.sum(np.arange(len(magnitude)) * magnitude) / np.sum(magnitude)
        spectral_rolloff = SignalProcessing._calculate_spectral_rolloff(magnitude)
        spectral_flux = np.sum(np.diff(magnitude) ** 2)
        
        return {
            'spectral_centroid': spectral_centroid / len(magnitude),
            'spectral_rolloff': spectral_rolloff / len(magnitude),
            'spectral_flux': spectral_flux,
            'energy': np.sum(power_spectrum),
            'entropy': SignalProcessing._calculate_spectral_entropy(magnitude)
        }
    
    @staticmethod
    def _calculate_spectral_rolloff(magnitude: np.ndarray, threshold: float = 0.85) -> float:
        """Calculate spectral rolloff point"""
        total_energy = np.sum(magnitude)
        cumulative_energy = np.cumsum(magnitude)
        rolloff_idx = np.where(cumulative_energy >= threshold * total_energy)[0]
        return rolloff_idx[0] if len(rolloff_idx) > 0 else len(magnitude) - 1
    
    @staticmethod
    def _calculate_spectral_entropy(magnitude: np.ndarray) -> float:
        """Calculate spectral entropy"""
        # Normalize to probability distribution
        prob_dist = magnitude / (np.sum(magnitude) + 1e-10)
        # Calculate entropy
        entropy = -np.sum(prob_dist * np.log2(prob_dist + 1e-10))
        return entropy
    
    @staticmethod
    def wavelet_transform(signal: np.ndarray, levels: int = 4) -> Dict[str, np.ndarray]:
        """Simple wavelet transform implementation"""
        # Haar wavelet implementation
        coefficients = {}
        current_signal = signal.copy()
        
        for level in range(levels):
            if len(current_signal) < 2:
                break
            
            # Downsample by averaging pairs
            length = len(current_signal) // 2 * 2
            current_signal = current_signal[:length]
            
            # Calculate approximation and detail coefficients
            approx = (current_signal[::2] + current_signal[1::2]) / 2
            detail = (current_signal[::2] - current_signal[1::2]) / 2
            
            coefficients[f'detail_{level}'] = detail
            current_signal = approx
        
        coefficients['approximation'] = current_signal
        return coefficients

class GameTheory:
    """Game theory applications for chess evaluation"""
    
    @staticmethod
    def calculate_nash_equilibrium_score(board: chess.Board) -> float:
        """Approximate Nash equilibrium evaluation"""
        # Get all legal moves for both sides
        white_moves = list(board.legal_moves) if board.turn == chess.WHITE else []
        
        # Switch turn to get black moves
        board_copy = board.copy()
        board_copy.turn = not board_copy.turn
        black_moves = list(board_copy.legal_moves)
        
        # Calculate move quality distribution
        white_quality = GameTheory._calculate_move_qualities(board, white_moves, chess.WHITE)
        black_quality = GameTheory._calculate_move_qualities(board, black_moves, chess.BLACK)
        
        # Nash equilibrium approximation
        white_expected = np.mean(white_quality) if white_quality else 0
        black_expected = np.mean(black_quality) if black_quality else 0
        
        return white_expected - black_expected
    
    @staticmethod
    def _calculate_move_qualities(board: chess.Board, moves: List[chess.Move], color: chess.Color) -> List[float]:
        """Calculate quality scores for moves"""
        qualities = []
        
        for move in moves:
            quality = 0.0
            
            # Capture value
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                if captured_piece:
                    piece_values = {chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
                                  chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0}
                    quality += piece_values.get(captured_piece.piece_type, 0)
            
            # Check value
            board.push(move)
            if board.is_check():
                quality += 0.5
            board.pop()
            
            # Central control
            center_squares = [chess.D4, chess.E4, chess.D5, chess.E5]
            if move.to_square in center_squares:
                quality += 0.3
            
            qualities.append(quality)
        
        return qualities
    
    @staticmethod
    def minimax_with_game_theory(board: chess.Board, depth: int, alpha: float = -float('inf'), 
                                beta: float = float('inf'), maximizing: bool = True) -> Tuple[float, Optional[chess.Move]]:
        """Enhanced minimax with game theory concepts"""
        if depth == 0 or board.is_game_over():
            return GameTheory._evaluate_terminal_position(board), None
        
        best_move = None
        
        if maximizing:
            max_eval = -float('inf')
            moves = list(board.legal_moves)
            
            # Order moves using game theory
            move_scores = []
            for move in moves:
                board.push(move)
                nash_score = GameTheory.calculate_nash_equilibrium_score(board)
                board.pop()
                move_scores.append((nash_score, move))
            
            # Sort by Nash equilibrium score
            move_scores.sort(key=lambda x: x[0], reverse=True)
            
            for _, move in move_scores:
                board.push(move)
                eval_score, _ = GameTheory.minimax_with_game_theory(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            moves = list(board.legal_moves)
            
            for move in moves:
                board.push(move)
                eval_score, _ = GameTheory.minimax_with_game_theory(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break
            
            return min_eval, best_move
    
    @staticmethod
    def _evaluate_terminal_position(board: chess.Board) -> float:
        """Evaluate terminal position"""
        if board.is_checkmate():
            return -1000 if board.turn == chess.WHITE else 1000
        elif board.is_stalemate() or board.is_insufficient_material():
            return 0
        else:
            # Use material balance as base evaluation
            piece_values = {chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
                          chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0}
            
            white_material = sum(piece_values.get(piece.piece_type, 0) 
                               for piece in board.piece_map().values() 
                               if piece.color == chess.WHITE)
            black_material = sum(piece_values.get(piece.piece_type, 0) 
                               for piece in board.piece_map().values() 
                               if piece.color == chess.BLACK)
            
            return white_material - black_material

class InformationTheory:
    """Information theory applications for chess"""
    
    @staticmethod
    def calculate_position_entropy(board: chess.Board, window_size: int = 8) -> float:
        """Calculate information entropy of position"""
        # Convert board to string representation
        board_str = str(board)
        
        # Calculate character frequency
        char_freq = {}
        for char in board_str:
            char_freq[char] = char_freq.get(char, 0) + 1
        
        # Calculate entropy
        total_chars = len(board_str)
        entropy = 0.0
        
        for freq in char_freq.values():
            probability = freq / total_chars
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    @staticmethod
    def mutual_information(board1: chess.Board, board2: chess.Board) -> float:
        """Calculate mutual information between two positions"""
        entropy1 = InformationTheory.calculate_position_entropy(board1)
        entropy2 = InformationTheory.calculate_position_entropy(board2)
        
        # Joint entropy approximation
        combined_str = str(board1) + str(board2)
        joint_entropy = InformationTheory._calculate_string_entropy(combined_str)
        
        # Mutual information = H(X) + H(Y) - H(X,Y)
        mutual_info = entropy1 + entropy2 - joint_entropy
        return max(0, mutual_info)  # Ensure non-negative
    
    @staticmethod
    def _calculate_string_entropy(text: str) -> float:
        """Calculate entropy of a string"""
        char_freq = {}
        for char in text:
            char_freq[char] = char_freq.get(char, 0) + 1
        
        total_chars = len(text)
        entropy = 0.0
        
        for freq in char_freq.values():
            probability = freq / total_chars
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy

class AdvancedMathematicalEvaluator:
    """Advanced evaluator combining all mathematical techniques"""
    
    def __init__(self, config: AdvancedEvaluationConfig = None):
        self.config = config or AdvancedEvaluationConfig()
        self.tensor_ops = TensorOperations()
        self.signal_proc = SignalProcessing()
        self.game_theory = GameTheory()
        self.info_theory = InformationTheory()
        self.pattern_kernels = self.tensor_ops.create_pattern_kernels()
        
        logger.info("Advanced mathematical evaluator initialized")
    
    def evaluate(self, board: chess.Board) -> float:
        """Comprehensive evaluation using all mathematical techniques"""
        total_score = 0.0
        weights = {}
        
        # 1. Tensor-based pattern recognition
        if self.config.use_tensor_operations:
            tensor_score = self._evaluate_with_tensors(board)
            total_score += tensor_score * 0.3
            weights['tensor'] = 0.3
        
        # 2. Signal processing analysis
        if self.config.use_signal_processing:
            signal_score = self._evaluate_with_signal_processing(board)
            total_score += signal_score * 0.25
            weights['signal'] = 0.25
        
        # 3. Game theory evaluation
        if self.config.use_game_theory:
            game_theory_score = self._evaluate_with_game_theory(board)
            total_score += game_theory_score * 0.25
            weights['game_theory'] = 0.25
        
        # 4. Information theory analysis
        if self.config.use_information_theory:
            info_score = self._evaluate_with_information_theory(board)
            total_score += info_score * 0.2
            weights['information'] = 0.2
        
        logger.debug(f"Evaluation weights: {weights}, Total score: {total_score}")
        return total_score
    
    def _evaluate_with_tensors(self, board: chess.Board) -> float:
        """Evaluate using tensor operations"""
        tensor = self.tensor_ops.board_to_tensor(board)
        score = 0.0
        
        # Apply pattern detection kernels
        for pattern_name, kernel in self.pattern_kernels.items():
            pattern_response = self.tensor_ops.tensor_convolution(tensor, kernel)
            pattern_strength = np.sum(pattern_response)
            
            # Weight different patterns
            pattern_weights = {
                'pawn_chain': 10.0,
                'coordination': 15.0,
                'center_control': 20.0,
                'edge_detection': 5.0
            }
            
            weight = pattern_weights.get(pattern_name, 1.0)
            score += pattern_strength * weight
        
        return score
    
    def _evaluate_with_signal_processing(self, board: chess.Board) -> float:
        """Evaluate using signal processing"""
        # Convert board to 1D signal
        tensor = self.tensor_ops.board_to_tensor(board)
        board_signal = tensor.flatten()
        
        # Fourier analysis
        fourier_features = self.signal_proc.fourier_analysis(board_signal, self.config.fourier_depth)
        
        # Wavelet analysis
        wavelet_coeffs = self.signal_proc.wavelet_transform(board_signal, self.config.wavelet_levels)
        
        # Combine features
        score = 0.0
        score += fourier_features['spectral_centroid'] * 100
        score += fourier_features['energy'] * 0.1
        score -= fourier_features['entropy'] * 50  # Lower entropy = more organized
        
        # Wavelet energy
        for level_name, coeffs in wavelet_coeffs.items():
            if 'detail' in level_name:
                score += np.sum(np.abs(coeffs)) * 5
        
        return score
    
    def _evaluate_with_game_theory(self, board: chess.Board) -> float:
        """Evaluate using game theory"""
        # Nash equilibrium approximation
        nash_score = self.game_theory.calculate_nash_equilibrium_score(board)
        
        # Enhanced minimax with limited depth for efficiency
        minimax_depth = max(1, int(self.config.minimax_depth_multiplier * 2))
        minimax_score, _ = self.game_theory.minimax_with_game_theory(board, minimax_depth)
        
        # Combine scores
        return nash_score * 50 + minimax_score * 10
    
    def _evaluate_with_information_theory(self, board: chess.Board) -> float:
        """Evaluate using information theory"""
        # Position entropy
        entropy = self.info_theory.calculate_position_entropy(board, self.config.entropy_window)
        
        # Information content (inverse of entropy for organized positions)
        information_content = 1.0 / (entropy + 1.0)
        
        # Complexity measure
        complexity = len(str(board).replace(' ', '').replace('\n', ''))
        
        return information_content * 100 - complexity * 0.5

# Export main class
__all__ = ['AdvancedMathematicalEvaluator', 'AdvancedEvaluationConfig']
