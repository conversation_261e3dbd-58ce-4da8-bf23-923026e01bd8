#!/usr/bin/env python3
"""
Advanced Tournament System untuk melawan multiple chess engines
Sistem otomatis untuk continuous training dan benchmarking
"""

import os
import pickle
import json
import time
import logging
import subprocess
import threading
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import chess
import chess.engine
import chess.pgn

from config import config
from advanced_neural_net import AdvancedNeuralEvaluator
from advanced_evaluation import AdvancedMathematicalEvaluator
from advanced_training import AdvancedReinforcementTrainer
from chess_engine import MinimaxEngine

logger = logging.getLogger(__name__)

@dataclass
class TournamentConfig:
    """Configuration for tournament system"""
    # Tournament settings
    games_per_engine: int = 10
    time_control: float = 5.0  # seconds per move
    max_game_length: int = 200
    
    # Training settings
    retrain_after_losses: int = 3
    max_training_iterations: int = 50
    training_games_per_iteration: int = 20
    
    # Engine settings
    engine_skill_levels: Dict[str, int] = None
    adaptive_difficulty: bool = True
    
    # Performance tracking
    target_win_rate: float = 0.7
    min_games_for_evaluation: int = 5
    
    def __post_init__(self):
        if self.engine_skill_levels is None:
            self.engine_skill_levels = {
                "stockfish": 10,  # Start with medium difficulty
                "ethereal": 15,
                "berserk": 15,
                "rubichess": 12,
                "minic": 8
            }

@dataclass
class GameResult:
    """Result of a single game"""
    engine_name: str
    our_color: chess.Color
    result: str  # 'win', 'loss', 'draw'
    moves: int
    time_taken: float
    final_position: str
    pgn: str
    timestamp: datetime

@dataclass
class EngineStats:
    """Statistics for performance against an engine"""
    engine_name: str
    games_played: int = 0
    wins: int = 0
    losses: int = 0
    draws: int = 0
    total_moves: int = 0
    total_time: float = 0.0
    current_skill_level: int = 10
    last_updated: datetime = None
    
    @property
    def win_rate(self) -> float:
        return self.wins / self.games_played if self.games_played > 0 else 0.0
    
    @property
    def average_game_length(self) -> float:
        return self.total_moves / self.games_played if self.games_played > 0 else 0.0

class EngineManager:
    """Manager untuk mengelola multiple chess engines"""
    
    def __init__(self):
        self.engines = {}
        self.engine_paths = {}
        self.load_engine_config()
    
    def load_engine_config(self):
        """Load engine configuration from file"""
        try:
            with open("engines_config.json", "r") as f:
                config_data = json.load(f)
                self.engine_paths = {
                    name: info["path"] 
                    for name, info in config_data.get("engines", {}).items()
                }
            logger.info(f"Loaded {len(self.engine_paths)} engines from config")
        except FileNotFoundError:
            logger.warning("engines_config.json not found, using default paths")
            self.engine_paths = {"stockfish": "/usr/games/stockfish"}
    
    def start_engine(self, engine_name: str, skill_level: int = 10) -> chess.engine.SimpleEngine:
        """Start a chess engine with specified skill level"""
        if engine_name not in self.engine_paths:
            raise ValueError(f"Engine {engine_name} not configured")
        
        engine_path = self.engine_paths[engine_name]
        
        try:
            engine = chess.engine.SimpleEngine.popen_uci(engine_path)
            
            # Configure engine skill level
            if engine_name == "stockfish":
                engine.configure({"Skill Level": skill_level})
                engine.configure({"Threads": 1})  # Use only 1 thread for fair comparison
            
            logger.info(f"Started {engine_name} at skill level {skill_level}")
            return engine
            
        except Exception as e:
            logger.error(f"Failed to start {engine_name}: {e}")
            raise
    
    def stop_engine(self, engine: chess.engine.SimpleEngine):
        """Stop a chess engine"""
        try:
            engine.quit()
        except:
            pass

class TournamentSystem:
    """Advanced tournament system for continuous training and benchmarking"""
    
    def __init__(self, config: TournamentConfig = None):
        self.config = config or TournamentConfig()
        self.engine_manager = EngineManager()
        
        # Initialize our AI components
        self.neural_evaluator = AdvancedNeuralEvaluator()
        self.math_evaluator = AdvancedMathematicalEvaluator()
        self.our_engine = MinimaxEngine(self.math_evaluator)
        self.trainer = AdvancedReinforcementTrainer()
        
        # Tournament state
        self.engine_stats = {}
        self.game_history = []
        self.tournament_active = False
        
        # Load previous stats if available
        self.load_tournament_stats()
        
        logger.info("Tournament system initialized")
    
    def run_continuous_tournament(self, max_rounds: int = 100):
        """Run continuous tournament with adaptive training"""
        logger.info(f"Starting continuous tournament for {max_rounds} rounds")
        self.tournament_active = True
        
        round_num = 1
        consecutive_losses = {}
        
        try:
            while round_num <= max_rounds and self.tournament_active:
                logger.info(f"\n=== Tournament Round {round_num} ===")
                
                # Play against all available engines
                round_results = self.play_tournament_round()
                
                # Analyze results and determine if retraining is needed
                needs_training = self.analyze_round_results(round_results, consecutive_losses)
                
                if needs_training:
                    logger.info("Performance below target, starting training session...")
                    self.run_training_session()
                    consecutive_losses.clear()  # Reset after training
                
                # Save progress
                self.save_tournament_stats()
                
                # Print current standings
                self.print_tournament_standings()
                
                round_num += 1
                
                # Check if we've achieved target performance against all engines
                if self.check_victory_condition():
                    logger.info("🎉 VICTORY! AI has achieved target win rate against all engines!")
                    break
        
        except KeyboardInterrupt:
            logger.info("Tournament interrupted by user")
        finally:
            self.tournament_active = False
            self.save_tournament_stats()
    
    def play_tournament_round(self) -> Dict[str, List[GameResult]]:
        """Play one round against all engines"""
        round_results = {}
        
        for engine_name in self.engine_manager.engine_paths.keys():
            logger.info(f"Playing against {engine_name}...")
            
            # Get current skill level for this engine
            current_stats = self.engine_stats.get(engine_name)
            if current_stats:
                skill_level = current_stats.current_skill_level
            else:
                skill_level = self.config.engine_skill_levels.get(engine_name, 10)
            
            # Play games against this engine
            engine_results = self.play_against_engine(engine_name, skill_level)
            round_results[engine_name] = engine_results
            
            # Update statistics
            self.update_engine_stats(engine_name, engine_results)
            
            # Adaptive difficulty adjustment
            if self.config.adaptive_difficulty:
                self.adjust_engine_difficulty(engine_name)
        
        return round_results
    
    def play_against_engine(self, engine_name: str, skill_level: int) -> List[GameResult]:
        """Play multiple games against a specific engine"""
        results = []
        engine = None
        
        try:
            engine = self.engine_manager.start_engine(engine_name, skill_level)
            
            for game_num in range(self.config.games_per_engine):
                # Alternate colors
                our_color = chess.WHITE if game_num % 2 == 0 else chess.BLACK
                
                logger.info(f"  Game {game_num + 1}/{self.config.games_per_engine} "
                           f"(playing as {'White' if our_color == chess.WHITE else 'Black'})")
                
                result = self.play_single_game(engine, engine_name, our_color)
                results.append(result)
                
                # Short break between games
                time.sleep(0.5)
        
        except Exception as e:
            logger.error(f"Error playing against {engine_name}: {e}")
        
        finally:
            if engine:
                self.engine_manager.stop_engine(engine)
        
        return results
    
    def play_single_game(self, opponent_engine: chess.engine.SimpleEngine, 
                        engine_name: str, our_color: chess.Color) -> GameResult:
        """Play a single game against an engine"""
        board = chess.Board()
        moves_played = 0
        start_time = time.time()
        game_pgn = chess.pgn.Game()
        node = game_pgn
        
        try:
            while not board.is_game_over() and moves_played < self.config.max_game_length:
                if board.turn == our_color:
                    # Our move
                    move = self.get_our_move(board)
                else:
                    # Opponent's move
                    move = self.get_opponent_move(opponent_engine, board)
                
                if move and move in board.legal_moves:
                    board.push(move)
                    node = node.add_variation(move)
                    moves_played += 1
                else:
                    logger.warning(f"Invalid move attempted: {move}")
                    break
            
            # Determine game result
            game_result = self.determine_game_result(board, our_color)
            
        except Exception as e:
            logger.error(f"Error during game: {e}")
            game_result = "draw"  # Default to draw on error
        
        end_time = time.time()
        
        return GameResult(
            engine_name=engine_name,
            our_color=our_color,
            result=game_result,
            moves=moves_played,
            time_taken=end_time - start_time,
            final_position=board.fen(),
            pgn=str(game_pgn),
            timestamp=datetime.now()
        )
    
    def get_our_move(self, board: chess.Board) -> chess.Move:
        """Get our AI's move"""
        try:
            # Use our advanced engine with time limit
            result = self.our_engine.search(board, depth=4)
            return result.move if result.move else None
        except Exception as e:
            logger.error(f"Error getting our move: {e}")
            # Fallback to random legal move
            legal_moves = list(board.legal_moves)
            return legal_moves[0] if legal_moves else None
    
    def get_opponent_move(self, engine: chess.engine.SimpleEngine, board: chess.Board) -> chess.Move:
        """Get opponent engine's move"""
        try:
            result = engine.play(board, chess.engine.Limit(time=self.config.time_control))
            return result.move
        except Exception as e:
            logger.error(f"Error getting opponent move: {e}")
            return None
    
    def determine_game_result(self, board: chess.Board, our_color: chess.Color) -> str:
        """Determine the result of the game from our perspective"""
        if board.is_checkmate():
            winner = not board.turn  # Previous player won
            if winner == our_color:
                return "win"
            else:
                return "loss"
        elif board.is_stalemate() or board.is_insufficient_material() or board.is_repetition():
            return "draw"
        else:
            # Game ended due to move limit
            return "draw"
    
    def update_engine_stats(self, engine_name: str, results: List[GameResult]):
        """Update statistics for an engine"""
        if engine_name not in self.engine_stats:
            self.engine_stats[engine_name] = EngineStats(engine_name=engine_name)
        
        stats = self.engine_stats[engine_name]
        
        for result in results:
            stats.games_played += 1
            stats.total_moves += result.moves
            stats.total_time += result.time_taken
            
            if result.result == "win":
                stats.wins += 1
            elif result.result == "loss":
                stats.losses += 1
            else:
                stats.draws += 1
        
        stats.last_updated = datetime.now()
        self.game_history.extend(results)
    
    def adjust_engine_difficulty(self, engine_name: str):
        """Adjust engine difficulty based on our performance"""
        stats = self.engine_stats.get(engine_name)
        if not stats or stats.games_played < self.config.min_games_for_evaluation:
            return
        
        win_rate = stats.win_rate
        current_level = stats.current_skill_level
        
        # Adjust difficulty to maintain challenge
        if win_rate > 0.8 and current_level < 20:
            # We're winning too much, increase difficulty
            stats.current_skill_level = min(20, current_level + 1)
            logger.info(f"Increased {engine_name} difficulty to level {stats.current_skill_level}")
        elif win_rate < 0.3 and current_level > 1:
            # We're losing too much, decrease difficulty
            stats.current_skill_level = max(1, current_level - 1)
            logger.info(f"Decreased {engine_name} difficulty to level {stats.current_skill_level}")
    
    def analyze_round_results(self, round_results: Dict[str, List[GameResult]], 
                            consecutive_losses: Dict[str, int]) -> bool:
        """Analyze round results and determine if training is needed"""
        needs_training = False
        
        for engine_name, results in round_results.items():
            losses_this_round = sum(1 for r in results if r.result == "loss")
            
            if losses_this_round > 0:
                consecutive_losses[engine_name] = consecutive_losses.get(engine_name, 0) + losses_this_round
                
                if consecutive_losses[engine_name] >= self.config.retrain_after_losses:
                    needs_training = True
                    logger.info(f"Too many losses against {engine_name}, training needed")
            else:
                consecutive_losses[engine_name] = 0
        
        return needs_training
    
    def run_training_session(self):
        """Run a training session to improve performance"""
        logger.info("Starting intensive training session...")
        
        training_results = self.trainer.train(
            num_iterations=self.config.max_training_iterations,
            games_per_iteration=self.config.training_games_per_iteration
        )
        
        # Update our engine with improved evaluator
        self.our_engine = MinimaxEngine(self.trainer.math_evaluator)
        
        logger.info(f"Training completed. Final win rate: {training_results.get('final_stats', {}).get('win_rate', 0):.3f}")
    
    def check_victory_condition(self) -> bool:
        """Check if we've achieved victory against all engines"""
        if not self.engine_stats:
            return False
        
        for engine_name, stats in self.engine_stats.items():
            if stats.games_played < self.config.min_games_for_evaluation:
                return False
            
            if stats.win_rate < self.config.target_win_rate:
                return False
        
        return True
    
    def print_tournament_standings(self):
        """Print current tournament standings"""
        print("\n" + "="*60)
        print("TOURNAMENT STANDINGS")
        print("="*60)
        
        for engine_name, stats in self.engine_stats.items():
            win_rate = stats.win_rate * 100
            print(f"{engine_name:15} | Games: {stats.games_played:3} | "
                  f"W: {stats.wins:2} L: {stats.losses:2} D: {stats.draws:2} | "
                  f"Win Rate: {win_rate:5.1f}% | Skill: {stats.current_skill_level:2}")
        
        print("="*60)
        
        # Overall statistics
        total_games = sum(stats.games_played for stats in self.engine_stats.values())
        total_wins = sum(stats.wins for stats in self.engine_stats.values())
        overall_win_rate = (total_wins / total_games * 100) if total_games > 0 else 0
        
        print(f"OVERALL: {total_games} games, {total_wins} wins, {overall_win_rate:.1f}% win rate")
        print("="*60)
    
    def save_tournament_stats(self):
        """Save tournament statistics to file"""
        data = {
            "engine_stats": {name: asdict(stats) for name, stats in self.engine_stats.items()},
            "game_history": [asdict(game) for game in self.game_history[-100:]],  # Keep last 100 games
            "config": asdict(self.config),
            "last_updated": datetime.now().isoformat()
        }
        
        os.makedirs("tournament_data", exist_ok=True)
        with open("tournament_data/tournament_stats.json", "w") as f:
            json.dump(data, f, indent=2, default=str)
    
    def load_tournament_stats(self):
        """Load tournament statistics from file"""
        try:
            with open("tournament_data/tournament_stats.json", "r") as f:
                data = json.load(f)
                
                # Load engine stats
                for name, stats_dict in data.get("engine_stats", {}).items():
                    stats_dict["last_updated"] = datetime.fromisoformat(stats_dict["last_updated"]) if stats_dict.get("last_updated") else None
                    self.engine_stats[name] = EngineStats(**stats_dict)
                
                logger.info(f"Loaded tournament stats for {len(self.engine_stats)} engines")
        
        except FileNotFoundError:
            logger.info("No previous tournament stats found, starting fresh")

def main():
    """Main tournament function"""
    print("Advanced Chess AI Tournament System")
    print("===================================")
    print("This system will continuously train our AI against multiple chess engines")
    print("until it achieves the target win rate against all of them.\n")
    
    # Create tournament system
    tournament = TournamentSystem()
    
    try:
        # Run continuous tournament
        tournament.run_continuous_tournament(max_rounds=50)
        
    except KeyboardInterrupt:
        print("\nTournament stopped by user")
    
    finally:
        # Print final results
        tournament.print_tournament_standings()

if __name__ == "__main__":
    main()
