"""
UCI (Universal Chess Interface) implementation
Allows the AI to communicate with chess GUIs and other engines
"""
import chess
import chess.engine
import chess.pgn
import sys
import threading
import time
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

from chess_engine import MinimaxEngine, MaterialEvaluator, HybridEvaluator, MoveEvaluation
from mcts import MCTSEngine, MCTSPredictor
from self_training import SelfTrainer
from config import config

logger = logging.getLogger(__name__)

@dataclass
class GameResult:
    """Container for game results"""
    white_player: str
    black_player: str
    result: str
    moves: List[str]
    game_length: int
    time_control: str
    timestamp: float
    analysis: Dict[str, Any]

class UCIEngine:
    """UCI-compliant chess engine"""
    
    def __init__(self):
        self.board = chess.Board()
        self.evaluator = HybridEvaluator()
        self.minimax_engine = MinimaxEngine(self.evaluator)
        self.mcts_engine = MCTSEngine(self.evaluator)
        self.predictor = MCTSPredictor(self.evaluator)
        
        # Engine settings
        self.search_depth = config.ai.minimax_depth
        self.time_limit = 5.0
        self.use_mcts = True
        self.debug = False
        
        # Game state
        self.game_history = []
        self.current_game_moves = []
        
    def uci_loop(self):
        """Main UCI communication loop"""
        logger.info("UCI engine started")
        
        while True:
            try:
                command = input().strip()
                if not command:
                    continue
                
                self.process_command(command)
                
            except EOFError:
                break
            except Exception as e:
                logger.error(f"Error processing command: {e}")
                if self.debug:
                    print(f"info string Error: {e}")
    
    def process_command(self, command: str):
        """Process UCI command"""
        parts = command.split()
        if not parts:
            return
        
        cmd = parts[0].lower()
        
        if cmd == "uci":
            self.cmd_uci()
        elif cmd == "isready":
            self.cmd_isready()
        elif cmd == "ucinewgame":
            self.cmd_ucinewgame()
        elif cmd == "position":
            self.cmd_position(parts[1:])
        elif cmd == "go":
            self.cmd_go(parts[1:])
        elif cmd == "stop":
            self.cmd_stop()
        elif cmd == "quit":
            self.cmd_quit()
        elif cmd == "setoption":
            self.cmd_setoption(parts[1:])
        elif cmd == "debug":
            self.cmd_debug(parts[1:])
        else:
            if self.debug:
                print(f"info string Unknown command: {command}")
    
    def cmd_uci(self):
        """Respond to UCI command"""
        print("id name Advanced Chess AI")
        print("id author AI Research Team")
        print()
        print("option name Debug type check default false")
        print("option name Search_Depth type spin default 4 min 1 max 10")
        print("option name Time_Limit type spin default 5000 min 100 max 30000")
        print("option name Use_MCTS type check default true")
        print("option name MCTS_Simulations type spin default 1000 min 100 max 10000")
        print()
        print("uciok")
    
    def cmd_isready(self):
        """Respond to isready command"""
        print("readyok")
    
    def cmd_ucinewgame(self):
        """Start new game"""
        self.board = chess.Board()
        self.current_game_moves = []
        if self.debug:
            print("info string New game started")
    
    def cmd_position(self, args: List[str]):
        """Set board position"""
        if not args:
            return
        
        if args[0] == "startpos":
            self.board = chess.Board()
            move_start = 1
        elif args[0] == "fen":
            # Find where moves start
            move_start = None
            for i, arg in enumerate(args):
                if arg == "moves":
                    move_start = i
                    break
            
            if move_start:
                fen = " ".join(args[1:move_start])
                move_start += 1
            else:
                fen = " ".join(args[1:])
                move_start = len(args)
            
            try:
                self.board = chess.Board(fen)
            except ValueError as e:
                if self.debug:
                    print(f"info string Invalid FEN: {e}")
                return
        else:
            return
        
        # Apply moves
        if move_start < len(args) and args[move_start - 1] == "moves":
            for move_str in args[move_start:]:
                try:
                    move = chess.Move.from_uci(move_str)
                    if move in self.board.legal_moves:
                        self.board.push(move)
                        self.current_game_moves.append(move_str)
                    else:
                        if self.debug:
                            print(f"info string Illegal move: {move_str}")
                        break
                except ValueError:
                    if self.debug:
                        print(f"info string Invalid move format: {move_str}")
                    break
    
    def cmd_go(self, args: List[str]):
        """Search for best move"""
        # Parse go command arguments
        time_limit = self.time_limit
        depth = self.search_depth
        
        i = 0
        while i < len(args):
            if args[i] == "depth" and i + 1 < len(args):
                depth = int(args[i + 1])
                i += 2
            elif args[i] == "movetime" and i + 1 < len(args):
                time_limit = int(args[i + 1]) / 1000.0  # Convert ms to seconds
                i += 2
            elif args[i] in ["wtime", "btime"] and i + 1 < len(args):
                remaining_time = int(args[i + 1]) / 1000.0
                # Use a fraction of remaining time
                time_limit = min(remaining_time / 30, 10.0)
                i += 2
            else:
                i += 1
        
        # Search for best move
        try:
            if self.use_mcts:
                best_move, confidence, pv = self.mcts_engine.search(
                    self.board, 
                    time_limit=time_limit
                )
                
                if self.debug:
                    print(f"info string MCTS confidence: {confidence:.3f}")
                    if pv:
                        pv_str = " ".join(str(move) for move in pv[:5])
                        print(f"info string PV: {pv_str}")
            else:
                result = self.minimax_engine.search(
                    self.board, 
                    depth=depth, 
                    time_limit=time_limit
                )
                best_move = result.move
                
                if self.debug:
                    print(f"info string Minimax score: {result.score:.2f}")
                    print(f"info string Nodes searched: {self.minimax_engine.nodes_searched}")
            
            if best_move:
                print(f"bestmove {best_move}")
            else:
                # No move found, return random legal move
                legal_moves = list(self.board.legal_moves)
                if legal_moves:
                    print(f"bestmove {legal_moves[0]}")
                else:
                    print("bestmove 0000")  # No legal moves
                    
        except Exception as e:
            logger.error(f"Error in search: {e}")
            if self.debug:
                print(f"info string Search error: {e}")
            
            # Fallback to random legal move
            legal_moves = list(self.board.legal_moves)
            if legal_moves:
                print(f"bestmove {legal_moves[0]}")
            else:
                print("bestmove 0000")
    
    def cmd_stop(self):
        """Stop current search"""
        # In a more sophisticated implementation, this would interrupt the search
        pass
    
    def cmd_quit(self):
        """Quit engine"""
        sys.exit(0)
    
    def cmd_setoption(self, args: List[str]):
        """Set engine option"""
        if len(args) < 4 or args[0] != "name" or args[2] != "value":
            return
        
        option_name = args[1].lower()
        option_value = args[3]
        
        try:
            if option_name == "debug":
                self.debug = option_value.lower() == "true"
            elif option_name == "search_depth":
                self.search_depth = int(option_value)
            elif option_name == "time_limit":
                self.time_limit = int(option_value) / 1000.0
            elif option_name == "use_mcts":
                self.use_mcts = option_value.lower() == "true"
            elif option_name == "mcts_simulations":
                config.ai.mcts_simulations = int(option_value)
            
            if self.debug:
                print(f"info string Set {option_name} = {option_value}")
                
        except ValueError:
            if self.debug:
                print(f"info string Invalid value for {option_name}: {option_value}")
    
    def cmd_debug(self, args: List[str]):
        """Set debug mode"""
        if args and args[0].lower() in ["on", "true"]:
            self.debug = True
        else:
            self.debug = False

class EngineManager:
    """Manager for running games against other engines"""
    
    def __init__(self):
        self.our_engine = UCIEngine()
        self.game_results: List[GameResult] = []
    
    def play_against_engine(self, engine_path: str, time_control: float = 5.0, 
                          our_color: chess.Color = chess.WHITE) -> GameResult:
        """Play a game against another engine"""
        logger.info(f"Starting game against {engine_path}")
        
        board = chess.Board()
        moves = []
        
        # Start opponent engine
        try:
            with chess.engine.SimpleEngine.popen_uci(engine_path) as opponent:
                
                while not board.is_game_over() and len(moves) < config.game.max_moves:
                    if board.turn == our_color:
                        # Our turn
                        if self.our_engine.use_mcts:
                            move, confidence, pv = self.our_engine.mcts_engine.search(
                                board, time_limit=time_control
                            )
                        else:
                            result = self.our_engine.minimax_engine.search(
                                board, depth=self.our_engine.search_depth, 
                                time_limit=time_control
                            )
                            move = result.move
                    else:
                        # Opponent's turn
                        result = opponent.play(
                            board, 
                            chess.engine.Limit(time=time_control)
                        )
                        move = result.move
                    
                    if move and move in board.legal_moves:
                        moves.append(move.uci())
                        board.push(move)
                        logger.debug(f"Move {len(moves)}: {move}")
                    else:
                        logger.error("Invalid move generated")
                        break
                
                # Analyze final position
                analysis = self._analyze_game(board, moves)
                
                game_result = GameResult(
                    white_player="Our AI" if our_color == chess.WHITE else os.path.basename(engine_path),
                    black_player="Our AI" if our_color == chess.BLACK else os.path.basename(engine_path),
                    result=board.result(),
                    moves=moves,
                    game_length=len(moves),
                    time_control=f"{time_control}s per move",
                    timestamp=time.time(),
                    analysis=analysis
                )
                
                self.game_results.append(game_result)
                logger.info(f"Game completed: {game_result.result}, {game_result.game_length} moves")
                
                return game_result
                
        except Exception as e:
            logger.error(f"Error playing against engine: {e}")
            raise
    
    def _analyze_game(self, board: chess.Board, moves: List[str]) -> Dict[str, Any]:
        """Analyze completed game"""
        analysis = {
            "final_position": board.fen(),
            "game_result": board.result(),
            "termination_reason": "checkmate" if board.is_checkmate() else
                                "stalemate" if board.is_stalemate() else
                                "insufficient_material" if board.is_insufficient_material() else
                                "max_moves" if len(moves) >= config.game.max_moves else "unknown",
            "move_count": len(moves),
            "material_balance": self._calculate_material_balance(board)
        }
        
        return analysis
    
    def _calculate_material_balance(self, board: chess.Board) -> int:
        """Calculate material balance"""
        material_values = {
            chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
            chess.ROOK: 5, chess.QUEEN: 9
        }
        
        balance = 0
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece and piece.piece_type != chess.KING:
                value = material_values[piece.piece_type]
                balance += value if piece.color == chess.WHITE else -value
        
        return balance
    
    def save_results(self, filename: str = None):
        """Save game results to JSON file"""
        if filename is None:
            filename = f"game_results_{int(time.time())}.json"
        
        filepath = os.path.join(config.logging.analysis_dir, filename)
        
        results_data = [asdict(result) for result in self.game_results]
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        logger.info(f"Game results saved to {filepath}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics from played games"""
        if not self.game_results:
            return {}
        
        wins = sum(1 for result in self.game_results if 
                  (result.white_player == "Our AI" and result.result == "1-0") or
                  (result.black_player == "Our AI" and result.result == "0-1"))
        
        losses = sum(1 for result in self.game_results if 
                    (result.white_player == "Our AI" and result.result == "0-1") or
                    (result.black_player == "Our AI" and result.result == "1-0"))
        
        draws = sum(1 for result in self.game_results if result.result == "1/2-1/2")
        
        total_games = len(self.game_results)
        win_rate = wins / total_games if total_games > 0 else 0
        
        avg_game_length = sum(result.game_length for result in self.game_results) / total_games
        
        return {
            "total_games": total_games,
            "wins": wins,
            "losses": losses,
            "draws": draws,
            "win_rate": win_rate,
            "average_game_length": avg_game_length
        }
