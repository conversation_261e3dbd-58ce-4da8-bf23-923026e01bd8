#!/usr/bin/env python3
"""
Model Persistence System untuk menyimpan dan memuat model AI yang sudah dilatih
Sistem ini memungkinkan model digunakan di aplikasi AI lainnya
"""

import os
import json
import pickle
import logging
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path

from advanced_neural_net import AdvancedNeuralEvaluator, AdvancedNetworkConfig
from advanced_evaluation import AdvancedMathematicalEvaluator, AdvancedEvaluationConfig
from advanced_training import AdvancedReinforcementTrainer, AdvancedTrainingConfig

logger = logging.getLogger(__name__)

@dataclass
class ModelMetadata:
    """Metadata untuk model yang disimpan"""
    model_id: str
    model_name: str
    model_type: str  # 'neural', 'mathematical', 'hybrid'
    version: str
    creation_date: datetime
    training_iterations: int
    performance_metrics: Dict[str, float]
    config: Dict[str, Any]
    file_size: int
    checksum: str
    description: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class ModelRegistry:
    """Registry untuk mengelola semua model yang tersimpan"""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        self.registry_file = self.models_dir / "model_registry.json"
        self.models = {}
        self.load_registry()
    
    def load_registry(self):
        """Load model registry dari file"""
        try:
            if self.registry_file.exists():
                with open(self.registry_file, 'r') as f:
                    data = json.load(f)
                    for model_id, metadata_dict in data.items():
                        metadata_dict['creation_date'] = datetime.fromisoformat(metadata_dict['creation_date'])
                        self.models[model_id] = ModelMetadata(**metadata_dict)
                logger.info(f"Loaded {len(self.models)} models from registry")
        except Exception as e:
            logger.error(f"Failed to load model registry: {e}")
            self.models = {}
    
    def save_registry(self):
        """Save model registry ke file"""
        try:
            data = {}
            for model_id, metadata in self.models.items():
                metadata_dict = asdict(metadata)
                metadata_dict['creation_date'] = metadata.creation_date.isoformat()
                data[model_id] = metadata_dict
            
            with open(self.registry_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved registry with {len(self.models)} models")
        except Exception as e:
            logger.error(f"Failed to save model registry: {e}")
    
    def register_model(self, metadata: ModelMetadata):
        """Register model baru"""
        self.models[metadata.model_id] = metadata
        self.save_registry()
    
    def get_model(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model metadata by ID"""
        return self.models.get(model_id)
    
    def list_models(self, model_type: str = None, tags: List[str] = None) -> List[ModelMetadata]:
        """List models dengan filter optional"""
        models = list(self.models.values())
        
        if model_type:
            models = [m for m in models if m.model_type == model_type]
        
        if tags:
            models = [m for m in models if any(tag in m.tags for tag in tags)]
        
        return sorted(models, key=lambda m: m.creation_date, reverse=True)
    
    def delete_model(self, model_id: str) -> bool:
        """Delete model dari registry dan file system"""
        if model_id not in self.models:
            return False
        
        try:
            # Delete model files
            model_file = self.models_dir / f"{model_id}.pkl"
            if model_file.exists():
                model_file.unlink()
            
            # Remove from registry
            del self.models[model_id]
            self.save_registry()
            
            logger.info(f"Deleted model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete model {model_id}: {e}")
            return False

class ModelPersistence:
    """Sistem untuk menyimpan dan memuat model AI"""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        self.registry = ModelRegistry(models_dir)
    
    def save_neural_model(self, evaluator: AdvancedNeuralEvaluator, 
                         model_name: str, performance_metrics: Dict[str, float],
                         description: str = "", tags: List[str] = None) -> str:
        """Save neural network model"""
        model_id = self._generate_model_id("neural", model_name)
        
        # Prepare model data
        model_data = {
            'type': 'neural',
            'evaluator': evaluator,
            'config': evaluator.config if hasattr(evaluator, 'config') else None
        }
        
        # Save to file
        model_file = self.models_dir / f"{model_id}.pkl"
        with open(model_file, 'wb') as f:
            pickle.dump(model_data, f)
        
        # Calculate checksum
        checksum = self._calculate_checksum(model_file)
        
        # Create metadata
        metadata = ModelMetadata(
            model_id=model_id,
            model_name=model_name,
            model_type="neural",
            version="1.0",
            creation_date=datetime.now(),
            training_iterations=performance_metrics.get('training_iterations', 0),
            performance_metrics=performance_metrics,
            config=asdict(evaluator.config) if hasattr(evaluator, 'config') else {},
            file_size=model_file.stat().st_size,
            checksum=checksum,
            description=description,
            tags=tags or []
        )
        
        # Register model
        self.registry.register_model(metadata)
        
        logger.info(f"Saved neural model: {model_id}")
        return model_id
    
    def save_mathematical_model(self, evaluator: AdvancedMathematicalEvaluator,
                               model_name: str, performance_metrics: Dict[str, float],
                               description: str = "", tags: List[str] = None) -> str:
        """Save mathematical model"""
        model_id = self._generate_model_id("mathematical", model_name)
        
        # Prepare model data
        model_data = {
            'type': 'mathematical',
            'evaluator': evaluator,
            'config': evaluator.config if hasattr(evaluator, 'config') else None
        }
        
        # Save to file
        model_file = self.models_dir / f"{model_id}.pkl"
        with open(model_file, 'wb') as f:
            pickle.dump(model_data, f)
        
        # Calculate checksum
        checksum = self._calculate_checksum(model_file)
        
        # Create metadata
        metadata = ModelMetadata(
            model_id=model_id,
            model_name=model_name,
            model_type="mathematical",
            version="1.0",
            creation_date=datetime.now(),
            training_iterations=performance_metrics.get('training_iterations', 0),
            performance_metrics=performance_metrics,
            config=asdict(evaluator.config) if hasattr(evaluator, 'config') else {},
            file_size=model_file.stat().st_size,
            checksum=checksum,
            description=description,
            tags=tags or []
        )
        
        # Register model
        self.registry.register_model(metadata)
        
        logger.info(f"Saved mathematical model: {model_id}")
        return model_id
    
    def save_hybrid_model(self, neural_evaluator: AdvancedNeuralEvaluator,
                         math_evaluator: AdvancedMathematicalEvaluator,
                         model_name: str, performance_metrics: Dict[str, float],
                         description: str = "", tags: List[str] = None) -> str:
        """Save hybrid model (neural + mathematical)"""
        model_id = self._generate_model_id("hybrid", model_name)
        
        # Prepare model data
        model_data = {
            'type': 'hybrid',
            'neural_evaluator': neural_evaluator,
            'math_evaluator': math_evaluator,
            'neural_config': neural_evaluator.config if hasattr(neural_evaluator, 'config') else None,
            'math_config': math_evaluator.config if hasattr(math_evaluator, 'config') else None
        }
        
        # Save to file
        model_file = self.models_dir / f"{model_id}.pkl"
        with open(model_file, 'wb') as f:
            pickle.dump(model_data, f)
        
        # Calculate checksum
        checksum = self._calculate_checksum(model_file)
        
        # Create metadata
        config_data = {}
        if hasattr(neural_evaluator, 'config'):
            config_data['neural'] = asdict(neural_evaluator.config)
        if hasattr(math_evaluator, 'config'):
            config_data['mathematical'] = asdict(math_evaluator.config)
        
        metadata = ModelMetadata(
            model_id=model_id,
            model_name=model_name,
            model_type="hybrid",
            version="1.0",
            creation_date=datetime.now(),
            training_iterations=performance_metrics.get('training_iterations', 0),
            performance_metrics=performance_metrics,
            config=config_data,
            file_size=model_file.stat().st_size,
            checksum=checksum,
            description=description,
            tags=tags or []
        )
        
        # Register model
        self.registry.register_model(metadata)
        
        logger.info(f"Saved hybrid model: {model_id}")
        return model_id
    
    def load_model(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Load model by ID"""
        metadata = self.registry.get_model(model_id)
        if not metadata:
            logger.error(f"Model {model_id} not found in registry")
            return None
        
        model_file = self.models_dir / f"{model_id}.pkl"
        if not model_file.exists():
            logger.error(f"Model file {model_file} not found")
            return None
        
        try:
            # Verify checksum
            current_checksum = self._calculate_checksum(model_file)
            if current_checksum != metadata.checksum:
                logger.warning(f"Checksum mismatch for model {model_id}")
            
            # Load model data
            with open(model_file, 'rb') as f:
                model_data = pickle.load(f)
            
            model_data['metadata'] = metadata
            logger.info(f"Loaded model: {model_id}")
            return model_data
            
        except Exception as e:
            logger.error(f"Failed to load model {model_id}: {e}")
            return None
    
    def export_model_for_deployment(self, model_id: str, export_path: str) -> bool:
        """Export model untuk deployment di aplikasi lain"""
        model_data = self.load_model(model_id)
        if not model_data:
            return False
        
        try:
            export_data = {
                'model_data': model_data,
                'deployment_info': {
                    'exported_at': datetime.now().isoformat(),
                    'model_id': model_id,
                    'version': model_data['metadata'].version,
                    'usage_instructions': self._generate_usage_instructions(model_data)
                }
            }
            
            with open(export_path, 'wb') as f:
                pickle.dump(export_data, f)
            
            logger.info(f"Exported model {model_id} to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export model {model_id}: {e}")
            return False
    
    def _generate_model_id(self, model_type: str, model_name: str) -> str:
        """Generate unique model ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name_hash = hashlib.md5(model_name.encode()).hexdigest()[:8]
        return f"{model_type}_{timestamp}_{name_hash}"
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _generate_usage_instructions(self, model_data: Dict[str, Any]) -> str:
        """Generate usage instructions for deployed model"""
        model_type = model_data.get('type', 'unknown')
        
        instructions = f"""
# Model Usage Instructions

## Model Type: {model_type}

### Loading the Model:
```python
import pickle

# Load the exported model
with open('model_file.pkl', 'rb') as f:
    exported_data = pickle.load(f)

model_data = exported_data['model_data']
"""
        
        if model_type == 'neural':
            instructions += """
# Get the neural evaluator
neural_evaluator = model_data['evaluator']

# Use for position evaluation
import chess
board = chess.Board()
score = neural_evaluator.evaluate(board)
```
"""
        elif model_type == 'mathematical':
            instructions += """
# Get the mathematical evaluator
math_evaluator = model_data['evaluator']

# Use for position evaluation
import chess
board = chess.Board()
score = math_evaluator.evaluate(board)
```
"""
        elif model_type == 'hybrid':
            instructions += """
# Get both evaluators
neural_evaluator = model_data['neural_evaluator']
math_evaluator = model_data['math_evaluator']

# Use for position evaluation
import chess
board = chess.Board()
neural_score = neural_evaluator.evaluate(board)
math_score = math_evaluator.evaluate(board)
combined_score = (neural_score + math_score) / 2
```
"""
        
        return instructions
    
    def list_models(self, model_type: str = None) -> List[ModelMetadata]:
        """List all available models"""
        return self.registry.list_models(model_type=model_type)
    
    def get_best_model(self, metric: str = 'win_rate') -> Optional[ModelMetadata]:
        """Get best performing model based on metric"""
        models = self.registry.list_models()
        if not models:
            return None
        
        best_model = max(models, 
                        key=lambda m: m.performance_metrics.get(metric, 0))
        return best_model

def create_model_snapshot(neural_evaluator: AdvancedNeuralEvaluator,
                         math_evaluator: AdvancedMathematicalEvaluator,
                         performance_metrics: Dict[str, float],
                         model_name: str = None) -> str:
    """Create snapshot of current model state"""
    if model_name is None:
        model_name = f"tournament_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    persistence = ModelPersistence()
    
    # Save hybrid model
    model_id = persistence.save_hybrid_model(
        neural_evaluator=neural_evaluator,
        math_evaluator=math_evaluator,
        model_name=model_name,
        performance_metrics=performance_metrics,
        description=f"Model snapshot with {performance_metrics.get('win_rate', 0):.1%} win rate",
        tags=["tournament", "snapshot", "hybrid"]
    )
    
    return model_id

def main():
    """Demo model persistence functionality"""
    print("Model Persistence System Demo")
    print("=============================")
    
    persistence = ModelPersistence()
    
    # List existing models
    models = persistence.list_models()
    print(f"\nFound {len(models)} existing models:")
    
    for model in models:
        print(f"  {model.model_id}: {model.model_name} ({model.model_type})")
        print(f"    Created: {model.creation_date}")
        print(f"    Win Rate: {model.performance_metrics.get('win_rate', 0):.1%}")
        print()
    
    # Show best model
    best_model = persistence.get_best_model('win_rate')
    if best_model:
        print(f"Best model: {best_model.model_name} with {best_model.performance_metrics.get('win_rate', 0):.1%} win rate")

if __name__ == "__main__":
    main()
